{"version": 3, "sources": ["../../../src/gel-core/columns/bigint.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyGelTable } from '~/gel-core/table.ts';\nimport { GelColumn } from './common.ts';\nimport { GelIntColumnBaseBuilder } from './int.common.ts';\n\nexport type GelInt53BuilderInitial<TName extends string> = GelInt53Builder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'GelInt53';\n\tdata: number;\n\tdriverParam: number;\n\tenumValues: undefined;\n}>;\n\nexport class GelInt53Builder<T extends ColumnBuilderBaseConfig<'number', 'GelInt53'>>\n\textends GelIntColumnBaseBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'GelInt53Builder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'GelInt53');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelInt53<MakeColumnConfig<T, TTableName>> {\n\t\treturn new GelInt53<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class GelInt53<T extends ColumnBaseConfig<'number', 'GelInt53'>> extends GelColumn<T> {\n\tstatic override readonly [entityKind]: string = 'GelInt53';\n\n\tgetSQLType(): string {\n\t\treturn 'bigint';\n\t}\n}\n\nexport function bigint(): GelInt53BuilderInitial<''>;\nexport function bigint<TName extends string>(name: TName): GelInt53BuilderInitial<TName>;\nexport function bigint(name?: string) {\n\treturn new GelInt53Builder(name ?? '');\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,iBAAiB;AAC1B,SAAS,+BAA+B;AAWjC,MAAM,wBACJ,wBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,UAAU;AAAA,EACjC;AAAA;AAAA,EAGS,MACR,OAC4C;AAC5C,WAAO,IAAI,SAA0C,OAAO,KAAK,MAA8C;AAAA,EAChH;AACD;AAEO,MAAM,iBAAmE,UAAa;AAAA,EAC5F,QAA0B,UAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,OAAO,MAAe;AACrC,SAAO,IAAI,gBAAgB,QAAQ,EAAE;AACtC;", "names": []}