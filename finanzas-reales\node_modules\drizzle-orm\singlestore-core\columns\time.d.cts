import type { ColumnBuilderBaseConfig } from "../../column-builder.cjs";
import type { ColumnBaseConfig } from "../../column.cjs";
import { entityKind } from "../../entity.cjs";
import { SingleStoreColumn, SingleStoreColumnBuilder } from "./common.cjs";
export type SingleStoreTimeBuilderInitial<TName extends string> = SingleStoreTimeBuilder<{
    name: TName;
    dataType: 'string';
    columnType: 'SingleStoreTime';
    data: string;
    driverParam: string | number;
    enumValues: undefined;
    generated: undefined;
}>;
export declare class SingleStoreTimeBuilder<T extends ColumnBuilderBaseConfig<'string', 'SingleStoreTime'>> extends SingleStoreColumnBuilder<T> {
    static readonly [entityKind]: string;
    constructor(name: T['name']);
}
export declare class SingleStoreTime<T extends ColumnBaseConfig<'string', 'SingleStoreTime'>> extends SingleStoreColumn<T> {
    static readonly [entityKind]: string;
    getSQLType(): string;
}
export declare function time(): SingleStoreTimeBuilderInitial<''>;
export declare function time<TName extends string>(name: TName): SingleStoreTimeBuilderInitial<TName>;
