import { db, User, BlogPost, FinancialProfile, FinancialStory, Resource, SiteConfig } from 'astro:db';
import bcrypt from 'bcryptjs';

export default async function seed() {
  // Crear usuario administrador por defecto
  const hashedPassword = await bcrypt.hash('admin123!', 10);
  
  await db.insert(User).values([
    {
      id: 1,
      email: '<EMAIL>',
      password: hashedPassword,
      name: '<PERSON>ministrador',
      role: 'admin',
      createdAt: new Date(),
    }
  ]);

  // Migrar perfiles financieros existentes
  await db.insert(FinancialProfile).values([
    {
      id: 1,
      profileId: 'deudas-estudiantiles',
      title: 'Deudas Estudiantiles',
      subtitle: 'Estrategias para eliminar préstamos educativos',
      description: 'Si tienes préstamos estudiantiles que parecen imposibles de pagar, no estás solo.',
      icon: 'graduation-cap',
      color: 'blue',
      situation: '<PERSON><PERSON>én graduados o profesionales jóvenes con deudas educativas significativas',
      challenges: JSON.stringify([
        'Pagos mínimos que consumen gran parte del salario',
        'Tasas de interés altas que hacen crecer la deuda',
        'Dificultad para ahorrar mientras se pagan las deudas',
        'Estrés por la carga financiera a largo plazo'
      ]),
      solutions: JSON.stringify([
        'Refinanciamiento estratégico para reducir tasas',
        'Métodos de pago acelerado (avalancha vs bola de nieve)',
        'Optimización del presupuesto para liberar dinero extra',
        'Estrategias de aumento de ingresos'
      ]),
      stats: JSON.stringify({
        averageDebt: '€35,000',
        averageTime: '8-12 años',
        successRate: '85%',
        averageSavings: '€15,000'
      }),
      featuredStory: JSON.stringify({
        name: 'María',
        achievement: 'Eliminó €45,000 en 3 años',
        savings: '€12,000 en intereses'
      }),
      resources: JSON.stringify([
        'Calculadora de refinanciamiento',
        'Plantilla de eliminación de deudas',
        'Guía de negociación con prestamistas'
      ]),
      published: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 2,
      profileId: 'familia-monoparental',
      title: 'Familia Monoparental',
      subtitle: 'Finanzas familiares con un solo ingreso',
      description: 'Criar una familia con un solo ingreso requiere estrategias financieras específicas.',
      icon: 'users',
      color: 'green',
      situation: 'Padres o madres solteros manejando todas las finanzas familiares',
      challenges: JSON.stringify([
        'Presupuesto ajustado con gastos familiares altos',
        'Falta de tiempo para gestión financiera',
        'Necesidad de fondo de emergencia más grande',
        'Planificación para educación de los hijos'
      ]),
      solutions: JSON.stringify([
        'Presupuesto por prioridades familiares',
        'Automatización máxima de finanzas',
        'Estrategias de ahorro en gastos familiares',
        'Planificación de seguros y protección'
      ]),
      stats: JSON.stringify({
        averageIncome: '€28,000',
        savingsRate: '15%',
        emergencyFund: '9 meses',
        successStories: '150+'
      }),
      featuredStory: JSON.stringify({
        name: 'Carmen',
        achievement: 'Ahorró €12,000 para educación',
        strategy: 'Presupuesto por sobres automatizado'
      }),
      resources: JSON.stringify([
        'Presupuesto familiar optimizado',
        'Calculadora de gastos por hijo',
        'Guía de seguros familiares'
      ]),
      published: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 3,
      profileId: 'ingresos-variables',
      title: 'Ingresos Variables',
      subtitle: 'Estabilidad financiera con ingresos impredecibles',
      description: 'Freelancers, emprendedores y trabajadores por comisión enfrentan desafíos únicos.',
      icon: 'trending-up',
      color: 'purple',
      situation: 'Profesionales independientes con ingresos que varían significativamente mes a mes',
      challenges: JSON.stringify([
        'Imposibilidad de presupuestar con métodos tradicionales',
        'Estrés financiero en meses de bajos ingresos',
        'Dificultad para ahorrar consistentemente',
        'Planificación fiscal compleja'
      ]),
      solutions: JSON.stringify([
        'Presupuesto por percentiles de ingresos',
        'Fondo de estabilización separado del de emergencia',
        'Automatización de ahorros por porcentajes',
        'Separación estricta de finanzas personales y de negocio'
      ]),
      stats: JSON.stringify({
        incomeVariation: '200-400%',
        stabilizationTime: '12-18 meses',
        averageSavings: '25%',
        stressReduction: '80%'
      }),
      featuredStory: JSON.stringify({
        name: 'Carlos',
        achievement: 'Estabilizó ingresos €2K-€8K',
        result: 'Fondo de €18,000 + inversiones regulares'
      }),
      resources: JSON.stringify([
        'Sistema de presupuesto por percentiles',
        'Calculadora de ingresos variables',
        'Guía de separación de finanzas'
      ]),
      published: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    }
  ]);

  // Migrar historias financieras existentes
  await db.insert(FinancialStory).values([
    {
      id: 1,
      storyId: 'maria-deudas-estudiantiles',
      title: 'De €45,000 en deudas estudiantiles a la libertad financiera',
      subtitle: 'Cómo María eliminó sus préstamos educativos en 3 años ahorrando €12,000 en intereses',
      profileId: 'deudas-estudiantiles',
      person: JSON.stringify({
        name: 'María González',
        age: 28,
        location: 'Madrid, España',
        occupation: 'Ingeniera de Software',
        familyStatus: 'Soltera, sin dependientes'
      }),
      situation: JSON.stringify({
        initialChallenge: 'Recién graduada con €45,000 en préstamos estudiantiles y un salario inicial de €35,000.',
        specificNumbers: {
          initialDebt: 45000,
          initialIncome: 35000,
          initialSavings: 1200,
          monthlyExpenses: 2100
        },
        timeframe: 'Enero 2021 - Marzo 2024',
        emotionalState: 'Abrumada por la deuda, preocupada por no poder ahorrar para otros objetivos.'
      }),
      strategy: JSON.stringify({
        mainApproach: 'Refinanciamiento estratégico + pagos adicionales focalizados',
        specificSteps: [
          'Refinanciar préstamos del 6.8% al 4.2% con SoFi',
          'Crear presupuesto detallado usando YNAB',
          'Automatizar pagos adicionales de €300/mes'
        ],
        toolsUsed: ['SoFi', 'YNAB', 'Transferencias automáticas']
      }),
      results: JSON.stringify({
        finalNumbers: {
          finalDebt: 0,
          finalIncome: 42000,
          finalSavings: 8000,
          totalSaved: 12000,
          timeToComplete: '3 años y 2 meses'
        },
        keyAchievements: [
          'Eliminó €45,000 en deudas estudiantiles',
          'Ahorró €12,000 en intereses',
          'Construyó fondo de emergencia de €8,000'
        ]
      }),
      resources: JSON.stringify({
        downloadableTemplate: 'Plan de Eliminación de Deudas Estudiantiles',
        calculator: 'Calculadora de Refinanciamiento',
        affiliateProducts: [
          {
            name: 'SoFi Student Loan Refinancing',
            category: 'Refinanciamiento',
            description: 'Plataforma de refinanciamiento con tasas competitivas',
            link: '/go/sofi-refinancing'
          }
        ]
      }),
      updates: JSON.stringify([]),
      tags: JSON.stringify(['deudas-estudiantiles', 'refinanciamiento', 'presupuesto']),
      featured: true,
      published: true,
      publishDate: new Date('2024-04-01'),
      lastUpdate: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
      authorId: 1,
    }
  ]);

  // Configuración inicial del sitio
  await db.insert(SiteConfig).values([
    {
      id: 1,
      key: 'site_title',
      value: 'FinanzasReales',
      description: 'Título principal del sitio',
      updatedAt: new Date(),
      updatedBy: 1,
    },
    {
      id: 2,
      key: 'admin_url',
      value: '/admin-finanzas-2024',
      description: 'URL secreta del panel de administración',
      updatedAt: new Date(),
      updatedBy: 1,
    },
    {
      id: 3,
      key: 'newsletter_enabled',
      value: 'true',
      description: 'Habilitar suscripción al newsletter',
      updatedAt: new Date(),
      updatedBy: 1,
    }
  ]);

  console.log('✅ Base de datos inicializada con datos de seed');
}
