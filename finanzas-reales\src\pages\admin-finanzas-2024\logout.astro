---
import { verifySession, destroySession } from '../../lib/auth';

// Verificar si hay una sesión activa
const authToken = Astro.cookies.get('auth-token')?.value;

if (authToken) {
  const session = await verifySession(authToken);
  if (session) {
    // Destruir la sesión en la base de datos
    await destroySession(session.sessionId);
  }
}

// Eliminar la cookie de autenticación
Astro.cookies.delete('auth-token', {
  path: '/'
});

// Redirigir al login
return Astro.redirect('/admin-finanzas-2024/login');
---
