---
// Component for finding stories by financial situation
---

<section class="bg-gray-50 py-16">
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
		<div class="text-center mb-12">
			<h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
				Encuentra historias por situación
			</h2>
			<p class="text-xl text-gray-600 max-w-3xl mx-auto">
				No buscamos por edad o ingresos, sino por los desafíos financieros específicos que enfrentas.
			</p>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
			<!-- Situación 1: Profesional con Deudas Estudiantiles -->
			<div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden">
				<div class="bg-gradient-to-r from-blue-500 to-blue-600 p-6">
					<div class="flex items-center">
						<div class="bg-white bg-opacity-20 rounded-lg p-3">
							<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
							</svg>
						</div>
						<div class="ml-4">
							<h3 class="text-xl font-bold text-white">Deudas Estudiantiles</h3>
							<p class="text-blue-100">Ingresos estables comprometidos</p>
						</div>
					</div>
				</div>
				<div class="p-6">
					<p class="text-gray-600 mb-4">
						Estrategias para equilibrar el pago de préstamos educativos con ahorro e inversión.
					</p>
					<div class="space-y-2 mb-6">
						<div class="flex items-center text-sm text-gray-500">
							<span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
							Refinanciamiento inteligente
						</div>
						<div class="flex items-center text-sm text-gray-500">
							<span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
							Optimización de pagos
						</div>
						<div class="flex items-center text-sm text-gray-500">
							<span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
							Ahorro paralelo
						</div>
					</div>
					<a href="/historias?perfil=deudas-estudiantiles" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium">
						Ver historias
						<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
						</svg>
					</a>
				</div>
			</div>

			<!-- Situación 2: Familia con Un Solo Ingreso -->
			<div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden">
				<div class="bg-gradient-to-r from-green-500 to-green-600 p-6">
					<div class="flex items-center">
						<div class="bg-white bg-opacity-20 rounded-lg p-3">
							<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
							</svg>
						</div>
						<div class="ml-4">
							<h3 class="text-xl font-bold text-white">Familia Monoparental</h3>
							<p class="text-green-100">Presupuesto ajustado</p>
						</div>
					</div>
				</div>
				<div class="p-6">
					<p class="text-gray-600 mb-4">
						Maximizar cada peso para seguridad familiar, educación de hijos y emergencias.
					</p>
					<div class="space-y-2 mb-6">
						<div class="flex items-center text-sm text-gray-500">
							<span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
							Presupuestos optimizados
						</div>
						<div class="flex items-center text-sm text-gray-500">
							<span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
							Seguros esenciales
						</div>
						<div class="flex items-center text-sm text-gray-500">
							<span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
							Ahorros creativos
						</div>
					</div>
					<a href="/historias?perfil=familia-monoparental" class="inline-flex items-center text-green-600 hover:text-green-700 font-medium">
						Ver historias
						<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
						</svg>
					</a>
				</div>
			</div>

			<!-- Situación 3: Emprendedor con Ingresos Variables -->
			<div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden">
				<div class="bg-gradient-to-r from-purple-500 to-purple-600 p-6">
					<div class="flex items-center">
						<div class="bg-white bg-opacity-20 rounded-lg p-3">
							<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
							</svg>
						</div>
						<div class="ml-4">
							<h3 class="text-xl font-bold text-white">Ingresos Variables</h3>
							<p class="text-purple-100">Flujo de caja irregular</p>
						</div>
					</div>
				</div>
				<div class="p-6">
					<p class="text-gray-600 mb-4">
						Estabilidad financiera para emprendedores y trabajadores independientes.
					</p>
					<div class="space-y-2 mb-6">
						<div class="flex items-center text-sm text-gray-500">
							<span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
							Fondos de emergencia flexibles
						</div>
						<div class="flex items-center text-sm text-gray-500">
							<span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
							Estrategias fiscales
						</div>
						<div class="flex items-center text-sm text-gray-500">
							<span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
							Protección patrimonial
						</div>
					</div>
					<a href="/historias?perfil=ingresos-variables" class="inline-flex items-center text-purple-600 hover:text-purple-700 font-medium">
						Ver historias
						<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
						</svg>
					</a>
				</div>
			</div>

			<!-- Situación 4: Transición de Carrera -->
			<div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden">
				<div class="bg-gradient-to-r from-orange-500 to-orange-600 p-6">
					<div class="flex items-center">
						<div class="bg-white bg-opacity-20 rounded-lg p-3">
							<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
							</svg>
						</div>
						<div class="ml-4">
							<h3 class="text-xl font-bold text-white">Cambio de Carrera</h3>
							<p class="text-orange-100">Ingresos reducidos temporalmente</p>
						</div>
					</div>
				</div>
				<div class="p-6">
					<p class="text-gray-600 mb-4">
						Mantener estabilidad durante transiciones profesionales e inversión en desarrollo.
					</p>
					<div class="space-y-2 mb-6">
						<div class="flex items-center text-sm text-gray-500">
							<span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
							Presupuestos de transición
						</div>
						<div class="flex items-center text-sm text-gray-500">
							<span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
							Uso estratégico de ahorros
						</div>
						<div class="flex items-center text-sm text-gray-500">
							<span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
							Inversión en formación
						</div>
					</div>
					<a href="/historias" class="inline-flex items-center text-orange-600 hover:text-orange-700 font-medium">
						Ver historias
						<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
						</svg>
					</a>
				</div>
			</div>

			<!-- Situación 5: Inversionista Principiante -->
			<div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden">
				<div class="bg-gradient-to-r from-teal-500 to-teal-600 p-6">
					<div class="flex items-center">
						<div class="bg-white bg-opacity-20 rounded-lg p-3">
							<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
							</svg>
						</div>
						<div class="ml-4">
							<h3 class="text-xl font-bold text-white">Primeras Inversiones</h3>
							<p class="text-teal-100">Ahorros disponibles</p>
						</div>
					</div>
				</div>
				<div class="p-6">
					<p class="text-gray-600 mb-4">
						Primeros pasos seguros en inversión, superando miedos y confusión inicial.
					</p>
					<div class="space-y-2 mb-6">
						<div class="flex items-center text-sm text-gray-500">
							<span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
							Estrategias conservadoras
						</div>
						<div class="flex items-center text-sm text-gray-500">
							<span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
							Diversificación básica
						</div>
						<div class="flex items-center text-sm text-gray-500">
							<span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
							Educación financiera
						</div>
					</div>
					<a href="/historias/primeras-inversiones" class="inline-flex items-center text-teal-600 hover:text-teal-700 font-medium">
						Ver historias
						<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
						</svg>
					</a>
				</div>
			</div>

			<!-- Situación 6: Eliminación de Deudas -->
			<div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden">
				<div class="bg-gradient-to-r from-red-500 to-red-600 p-6">
					<div class="flex items-center">
						<div class="bg-white bg-opacity-20 rounded-lg p-3">
							<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
							</svg>
						</div>
						<div class="ml-4">
							<h3 class="text-xl font-bold text-white">Salir de Deudas</h3>
							<p class="text-red-100">Múltiples compromisos</p>
						</div>
					</div>
				</div>
				<div class="p-6">
					<p class="text-gray-600 mb-4">
						Planes sistemáticos para eliminar deudas manteniendo calidad de vida.
					</p>
					<div class="space-y-2 mb-6">
						<div class="flex items-center text-sm text-gray-500">
							<span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
							Método bola de nieve
						</div>
						<div class="flex items-center text-sm text-gray-500">
							<span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
							Consolidación inteligente
						</div>
						<div class="flex items-center text-sm text-gray-500">
							<span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
							Ingresos adicionales
						</div>
					</div>
					<a href="/historias/eliminar-deudas" class="inline-flex items-center text-red-600 hover:text-red-700 font-medium">
						Ver historias
						<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
						</svg>
					</a>
				</div>
			</div>
		</div>

		<!-- CTA Section -->
		<div class="text-center mt-12">
			<p class="text-lg text-gray-600 mb-6">
				¿No encuentras tu situación específica?
			</p>
			<a href="/perfiles" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200 shadow-lg hover:shadow-xl">
				Explorar Todos los Perfiles
			</a>
		</div>
	</div>
</section>
