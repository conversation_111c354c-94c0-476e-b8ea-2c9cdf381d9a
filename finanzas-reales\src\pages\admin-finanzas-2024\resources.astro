---
import { verifySession } from '../../lib/auth';
import AdminLayout from '../../layouts/AdminLayout.astro';

// Verificar autenticación
const authToken = Astro.cookies.get('auth-token')?.value;
if (!authToken) {
  return Astro.redirect('/admin-finanzas-2024/login');
}

const session = await verifySession(authToken);
if (!session) {
  Astro.cookies.delete('auth-token');
  return Astro.redirect('/admin-finanzas-2024/login');
}
---

<AdminLayout title="Gestión de Recursos - Admin FinanzasReales">
  <!-- Navigation -->
  <nav class="admin-nav">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center space-x-4">
          <a href="/admin-finanzas-2024/dashboard" class="text-xl font-semibold text-gray-900">
            FinanzasReales Admin
          </a>
          <span class="text-gray-400">|</span>
          <span class="text-gray-600">Recursos</span>
        </div>
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-700">
            {session.name}
          </span>
          <a 
            href="/admin-finanzas-2024/logout" 
            class="text-sm text-red-600 hover:text-red-800"
          >
            Cerrar Sesión
          </a>
        </div>
      </div>
    </div>
  </nav>

  <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
    <!-- Page header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">
        Gestión de Recursos
      </h1>
      <p class="text-gray-600 text-lg">
        Administra plantillas, guías y recursos descargables
      </p>
    </div>

    <!-- Coming Soon Message -->
    <div class="bg-white rounded-xl shadow-lg p-8 text-center">
      <div class="max-w-md mx-auto">
        <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
          </svg>
        </div>
        <h2 class="text-xl font-bold text-gray-900 mb-2">
          Gestión de Recursos
        </h2>
        <p class="text-gray-600 mb-6">
          Esta funcionalidad está en desarrollo. Pronto podrás gestionar todos los recursos desde aquí.
        </p>
        <div class="space-y-3">
          <p class="text-sm text-gray-500">
            <strong>Funcionalidades próximas:</strong>
          </p>
          <ul class="text-sm text-gray-500 space-y-1">
            <li>• Subir plantillas y guías</li>
            <li>• Gestionar recursos descargables</li>
            <li>• Organizar por categorías</li>
            <li>• Estadísticas de descargas</li>
          </ul>
        </div>
        <div class="mt-6">
          <a 
            href="/admin-finanzas-2024/dashboard"
            class="inline-flex items-center px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg transition-colors"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Volver al Dashboard
          </a>
        </div>
      </div>
    </div>
  </div>
</AdminLayout>
