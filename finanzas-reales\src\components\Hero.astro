---
// Hero component for the main page
---

<section class="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
		<div class="text-center">
			<h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
				Encuentra tu 
				<span class="text-blue-600">historia financiera</span>
			</h1>
			<p class="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto">
				Historias reales de personas como tú que transformaron sus finanzas. 
				Descubre estrategias probadas basadas en situaciones auténticas.
			</p>
			
			<!-- Search by situation -->
			<div class="max-w-2xl mx-auto mb-12">
				<div class="bg-white rounded-lg shadow-lg p-6">
					<h3 class="text-lg font-semibold text-gray-900 mb-4">
						¿Cuál es tu situación financiera actual?
					</h3>
					<div class="grid grid-cols-1 md:grid-cols-2 gap-3">
						<button class="situation-btn bg-gray-50 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg p-4 text-left transition-all duration-200">
							<div class="font-medium text-gray-900">Tengo deudas estudiantiles</div>
							<div class="text-sm text-gray-600">Estrategias de pago y refinanciamiento</div>
						</button>
						<button class="situation-btn bg-gray-50 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg p-4 text-left transition-all duration-200">
							<div class="font-medium text-gray-900">Familia con un solo ingreso</div>
							<div class="text-sm text-gray-600">Maximizar presupuesto familiar</div>
						</button>
						<button class="situation-btn bg-gray-50 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg p-4 text-left transition-all duration-200">
							<div class="font-medium text-gray-900">Ingresos variables</div>
							<div class="text-sm text-gray-600">Estabilidad para emprendedores</div>
						</button>
						<button class="situation-btn bg-gray-50 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg p-4 text-left transition-all duration-200">
							<div class="font-medium text-gray-900">Cambio de carrera</div>
							<div class="text-sm text-gray-600">Finanzas durante transiciones</div>
						</button>
						<button class="situation-btn bg-gray-50 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg p-4 text-left transition-all duration-200">
							<div class="font-medium text-gray-900">Quiero empezar a invertir</div>
							<div class="text-sm text-gray-600">Primeros pasos seguros</div>
						</button>
						<button class="situation-btn bg-gray-50 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg p-4 text-left transition-all duration-200">
							<div class="font-medium text-gray-900">Salir de deudas</div>
							<div class="text-sm text-gray-600">Planes de eliminación de deuda</div>
						</button>
					</div>
				</div>
			</div>

			<!-- CTA Buttons -->
			<div class="flex flex-col sm:flex-row gap-4 justify-center">
				<a href="/historias" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200 shadow-lg hover:shadow-xl">
					Ver Todas las Historias
				</a>
				<a href="/calculadoras" class="bg-white hover:bg-gray-50 text-blue-600 font-semibold py-3 px-8 rounded-lg border-2 border-blue-600 transition-colors duration-200">
					Usar Calculadoras
				</a>
			</div>
		</div>
	</div>
</section>

<!-- Stats Section -->
<section class="bg-white py-16">
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
		<div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
			<div class="bg-gray-50 rounded-lg p-8">
				<div class="text-3xl font-bold text-blue-600 mb-2">150+</div>
				<div class="text-gray-600">Historias Reales</div>
				<div class="text-sm text-gray-500 mt-2">Casos documentados con resultados verificables</div>
			</div>
			<div class="bg-gray-50 rounded-lg p-8">
				<div class="text-3xl font-bold text-green-600 mb-2">€2.3M</div>
				<div class="text-gray-600">Deuda Eliminada</div>
				<div class="text-sm text-gray-500 mt-2">Total de deudas pagadas por nuestros usuarios</div>
			</div>
			<div class="bg-gray-50 rounded-lg p-8">
				<div class="text-3xl font-bold text-purple-600 mb-2">€890K</div>
				<div class="text-gray-600">Ahorros Generados</div>
				<div class="text-sm text-gray-500 mt-2">Dinero ahorrado siguiendo nuestras estrategias</div>
			</div>
		</div>
	</div>
</section>

<style>
	.situation-btn:hover {
		transform: translateY(-2px);
		box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
	}
</style>

<script>
	// Add click handlers for situation buttons
	document.addEventListener('DOMContentLoaded', function() {
		const situationBtns = document.querySelectorAll('.situation-btn');
		
		situationBtns.forEach(btn => {
			btn.addEventListener('click', function() {
				// Remove active class from all buttons
				situationBtns.forEach(b => b.classList.remove('bg-blue-100', 'border-blue-500'));
				
				// Add active class to clicked button
				this.classList.add('bg-blue-100', 'border-blue-500');
				
				// Here you could add logic to filter content or navigate to specific sections
				const situation = this.querySelector('.font-medium').textContent;
				console.log('Selected situation:', situation);
			});
		});
	});
</script>
