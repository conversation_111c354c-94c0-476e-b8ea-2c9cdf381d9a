---
import { verifySession } from '../../lib/auth';
import AdminLayout from '../../layouts/AdminLayout.astro';

// Verificar autenticación
const authToken = Astro.cookies.get('auth-token')?.value;
if (!authToken) {
  return Astro.redirect('/admin-finanzas-2024/login');
}

const session = await verifySession(authToken);
if (!session) {
  Astro.cookies.delete('auth-token');
  return Astro.redirect('/admin-finanzas-2024/login');
}

// Estadísticas del dashboard (datos de ejemplo por ahora)
const stats = {
  totalBlogPosts: 12,
  publishedBlogPosts: 8,
  draftBlogPosts: 4,
  totalStories: 6,
  publishedStories: 5,
  featuredStories: 3,
  totalProfiles: 6,
  totalResources: 15,
};

const recentBlogPosts = [
  { id: 1, title: "Estrategias para eliminar deudas estudiantiles", published: true, updatedAt: new Date() },
  { id: 2, title: "Presupuesto para ingresos variables", published: false, updatedAt: new Date() },
  { id: 3, title: "Automatización financiera para familias", published: true, updatedAt: new Date() },
];

const recentStories = [
  { id: 1, title: "María elimina €45,000 en deudas estudiantiles", published: true, updatedAt: new Date() },
  { id: 2, title: "Carlos estabiliza sus ingresos variables", published: true, updatedAt: new Date() },
  { id: 3, title: "Carmen ahorra para la educación de sus hijos", published: false, updatedAt: new Date() },
];
---

<AdminLayout title="Dashboard - Admin FinanzasReales">
<body class="min-h-screen">
  <!-- Navigation -->
  <nav class="admin-nav">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <!-- Logo y título -->
        <div class="flex items-center space-x-4">
          <div class="flex items-center">
            <div class="w-8 h-8 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center mr-3">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
              </svg>
            </div>
            <h1 class="text-xl font-bold text-gray-900">
              FinanzasReales
            </h1>
          </div>
          <span class="hidden sm:block text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
            Admin
          </span>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:flex items-center space-x-6">
          <a href="/admin-finanzas-2024/dashboard" class="text-emerald-600 font-medium">Dashboard</a>
          <a href="/admin-finanzas-2024/blog" class="text-gray-600 hover:text-emerald-600 transition-colors">Blog</a>
          <a href="/admin-finanzas-2024/stories" class="text-gray-600 hover:text-emerald-600 transition-colors">Historias</a>
          <a href="/admin-finanzas-2024/profiles" class="text-gray-600 hover:text-emerald-600 transition-colors">Perfiles</a>
          <a href="/admin-finanzas-2024/resources" class="text-gray-600 hover:text-emerald-600 transition-colors">Recursos</a>
        </div>

        <!-- User menu -->
        <div class="flex items-center space-x-4">
          <div class="hidden sm:flex items-center space-x-3">
            <div class="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
              <span class="text-emerald-600 font-medium text-sm">{session.name.charAt(0).toUpperCase()}</span>
            </div>
            <span class="text-sm text-gray-700">
              {session.name}
            </span>
          </div>
          <a
            href="/admin-finanzas-2024/logout"
            class="text-sm text-red-600 hover:text-red-800 font-medium transition-colors"
          >
            Salir
          </a>

          <!-- Mobile menu button -->
          <button class="md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100" onclick="toggleMobileMenu()">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </nav>

  <!-- Mobile Menu -->
  <div class="mobile-menu" id="mobileMenu" onclick="closeMobileMenu(event)">
    <div class="mobile-menu-content p-6">
      <div class="flex items-center justify-between mb-8">
        <h2 class="text-lg font-bold text-gray-900">Menú</h2>
        <button onclick="toggleMobileMenu()" class="p-2 rounded-md text-gray-600 hover:text-gray-900">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      <nav class="space-y-4">
        <a href="/admin-finanzas-2024/dashboard" class="block text-emerald-600 font-medium py-2">Dashboard</a>
        <a href="/admin-finanzas-2024/blog" class="block text-gray-600 hover:text-emerald-600 py-2">Blog</a>
        <a href="/admin-finanzas-2024/stories" class="block text-gray-600 hover:text-emerald-600 py-2">Historias</a>
        <a href="/admin-finanzas-2024/profiles" class="block text-gray-600 hover:text-emerald-600 py-2">Perfiles</a>
        <a href="/admin-finanzas-2024/resources" class="block text-gray-600 hover:text-emerald-600 py-2">Recursos</a>
      </nav>
    </div>
  </div>

  <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
    <!-- Page header -->
    <div class="fade-in mb-8">
      <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
          <h1 class="text-3xl font-bold text-gray-900 mb-2">
            Dashboard
          </h1>
          <p class="text-gray-600 text-lg">
            Gestiona el contenido de FinanzasReales
          </p>
        </div>
        <div class="mt-4 md:mt-0 flex space-x-3">
          <a
            href="/"
            target="_blank"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
            </svg>
            Ver Sitio
          </a>
        </div>
      </div>
    </div>

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 slide-up">
      <!-- Blog Stats -->
      <div class="stat-card p-6">
        <div class="flex items-center">
          <div class="stat-icon bg-gradient-to-br from-blue-500 to-blue-600">
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
            </svg>
          </div>
          <div class="ml-4 flex-1">
            <h3 class="text-sm font-medium text-gray-500 mb-1">
              Artículos del Blog
            </h3>
            <p class="text-2xl font-bold text-gray-900">
              {stats.totalBlogPosts}
            </p>
            <p class="text-sm text-gray-600">
              {stats.publishedBlogPosts} publicados • {stats.draftBlogPosts} borradores
            </p>
          </div>
        </div>
        <div class="mt-4 pt-4 border-t border-gray-100">
          <a href="/admin-finanzas-2024/blog" class="text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors">
            Gestionar Blog →
          </a>
        </div>
      </div>

      <!-- Stories Stats -->
      <div class="stat-card p-6">
        <div class="flex items-center">
          <div class="stat-icon bg-gradient-to-br from-emerald-500 to-emerald-600">
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
            </svg>
          </div>
          <div class="ml-4 flex-1">
            <h3 class="text-sm font-medium text-gray-500 mb-1">
              Historias Financieras
            </h3>
            <p class="text-2xl font-bold text-gray-900">
              {stats.totalStories}
            </p>
            <p class="text-sm text-gray-600">
              {stats.publishedStories} publicadas • {stats.featuredStories} destacadas
            </p>
          </div>
        </div>
        <div class="mt-4 pt-4 border-t border-gray-100">
          <a href="/admin-finanzas-2024/stories" class="text-sm font-medium text-emerald-600 hover:text-emerald-700 transition-colors">
            Gestionar Historias →
          </a>
        </div>
      </div>

      <!-- Profiles Stats -->
      <div class="stat-card p-6">
        <div class="flex items-center">
          <div class="stat-icon bg-gradient-to-br from-purple-500 to-purple-600">
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
          </div>
          <div class="ml-4 flex-1">
            <h3 class="text-sm font-medium text-gray-500 mb-1">
              Perfiles Financieros
            </h3>
            <p class="text-2xl font-bold text-gray-900">
              {stats.totalProfiles}
            </p>
            <p class="text-sm text-gray-600">
              Situaciones financieras
            </p>
          </div>
        </div>
        <div class="mt-4 pt-4 border-t border-gray-100">
          <a href="/admin-finanzas-2024/profiles" class="text-sm font-medium text-purple-600 hover:text-purple-700 transition-colors">
            Gestionar Perfiles →
          </a>
        </div>
      </div>

      <!-- Resources Stats -->
      <div class="stat-card p-6">
        <div class="flex items-center">
          <div class="stat-icon bg-gradient-to-br from-orange-500 to-orange-600">
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
            </svg>
          </div>
          <div class="ml-4 flex-1">
            <h3 class="text-sm font-medium text-gray-500 mb-1">
              Recursos
            </h3>
            <p class="text-2xl font-bold text-gray-900">
              {stats.totalResources}
            </p>
            <p class="text-sm text-gray-600">
              Plantillas y guías
            </p>
          </div>
        </div>
        <div class="mt-4 pt-4 border-t border-gray-100">
          <a href="/admin-finanzas-2024/resources" class="text-sm font-medium text-orange-600 hover:text-orange-700 transition-colors">
            Gestionar Recursos →
          </a>
        </div>
      </div>
    </div>



    <!-- Recent Content -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Recent Blog Posts -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            Artículos Recientes
          </h3>
          <div class="space-y-3">
            {recentBlogPosts.map(post => (
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <p class="text-sm font-medium text-gray-900 truncate">
                    {post.title}
                  </p>
                  <p class="text-sm text-gray-500">
                    {post.published ? 'Publicado' : 'Borrador'} • {new Date(post.updatedAt).toLocaleDateString('es-ES')}
                  </p>
                </div>
                <a 
                  href={`/admin-finanzas-2024/blog/edit/${post.id}`}
                  class="text-blue-600 hover:text-blue-500 text-sm"
                >
                  Editar
                </a>
              </div>
            ))}
          </div>
          <div class="mt-4">
            <a href="/admin-finanzas-2024/blog" class="text-sm text-blue-600 hover:text-blue-500">
              Ver todos los artículos →
            </a>
          </div>
        </div>
      </div>

      <!-- Recent Stories -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            Historias Recientes
          </h3>
          <div class="space-y-3">
            {recentStories.map(story => (
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <p class="text-sm font-medium text-gray-900 truncate">
                    {story.title}
                  </p>
                  <p class="text-sm text-gray-500">
                    {story.published ? 'Publicada' : 'Borrador'} • {new Date(story.updatedAt).toLocaleDateString('es-ES')}
                  </p>
                </div>
                <a 
                  href={`/admin-finanzas-2024/stories/edit/${story.id}`}
                  class="text-green-600 hover:text-green-500 text-sm"
                >
                  Editar
                </a>
              </div>
            ))}
          </div>
          <div class="mt-4">
            <a href="/admin-finanzas-2024/stories" class="text-sm text-green-600 hover:text-green-500">
              Ver todas las historias →
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Funciones para el menú móvil
    function toggleMobileMenu() {
      const menu = document.getElementById('mobileMenu');
      if (menu) {
        menu.classList.toggle('active');
      }
    }

    function closeMobileMenu(event: Event) {
      if (event.target === event.currentTarget) {
        const menu = document.getElementById('mobileMenu');
        if (menu) {
          menu.classList.remove('active');
        }
      }
    }

    // Animaciones de entrada
    document.addEventListener('DOMContentLoaded', function() {
      // Fade in para el header
      const fadeElements = document.querySelectorAll('.fade-in');
      fadeElements.forEach((el, index) => {
        const element = el as HTMLElement;
        element.style.opacity = '0';
        setTimeout(() => {
          element.style.transition = 'opacity 0.6s ease-out';
          element.style.opacity = '1';
        }, index * 100);
      });

      // Slide up para las tarjetas
      const slideElements = document.querySelectorAll('.slide-up');
      slideElements.forEach((el, index) => {
        const element = el as HTMLElement;
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        setTimeout(() => {
          element.style.transition = 'all 0.8s ease-out';
          element.style.opacity = '1';
          element.style.transform = 'translateY(0)';
        }, 200 + (index * 150));
      });
    });

    // Cerrar menú móvil al hacer clic en enlaces
    document.querySelectorAll('.mobile-menu a').forEach(link => {
      link.addEventListener('click', () => {
        const menu = document.getElementById('mobileMenu');
        if (menu) {
          menu.classList.remove('active');
        }
      });
    });

    // Hacer funciones globales
    (window as any).toggleMobileMenu = toggleMobileMenu;
    (window as any).closeMobileMenu = closeMobileMenu;
  </script>
</AdminLayout>
