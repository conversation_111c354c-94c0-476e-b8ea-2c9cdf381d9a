---
import { verifySession } from '../../lib/auth';
import { db, BlogPost, FinancialStory, FinancialProfile, Resource } from 'astro:db';

// Verificar autenticación
const authToken = Astro.cookies.get('auth-token')?.value;
if (!authToken) {
  return Astro.redirect('/admin-finanzas-2024/login');
}

const session = await verifySession(authToken);
if (!session) {
  Astro.cookies.delete('auth-token');
  return Astro.redirect('/admin-finanzas-2024/login');
}

// Obtener estadísticas del dashboard
const [blogPosts, stories, profiles, resources] = await Promise.all([
  db.select().from(BlogPost),
  db.select().from(FinancialStory),
  db.select().from(FinancialProfile),
  db.select().from(Resource)
]);

const stats = {
  totalBlogPosts: blogPosts.length,
  publishedBlogPosts: blogPosts.filter(post => post.published).length,
  draftBlogPosts: blogPosts.filter(post => !post.published).length,
  totalStories: stories.length,
  publishedStories: stories.filter(story => story.published).length,
  featuredStories: stories.filter(story => story.featured).length,
  totalProfiles: profiles.length,
  totalResources: resources.length,
};

const recentBlogPosts = blogPosts
  .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
  .slice(0, 5);

const recentStories = stories
  .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
  .slice(0, 5);
---

<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dashboard - Admin FinanzasReales</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
  <!-- Navigation -->
  <nav class="bg-white shadow-sm border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <h1 class="text-xl font-semibold text-gray-900">
            FinanzasReales Admin
          </h1>
        </div>
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-700">
            Bienvenido, {session.name}
          </span>
          <a 
            href="/admin-finanzas-2024/logout" 
            class="text-sm text-red-600 hover:text-red-800"
          >
            Cerrar Sesión
          </a>
        </div>
      </div>
    </div>
  </nav>

  <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Page header -->
    <div class="md:flex md:items-center md:justify-between mb-8">
      <div class="flex-1 min-w-0">
        <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
          Dashboard
        </h2>
        <p class="mt-1 text-sm text-gray-500">
          Gestiona el contenido de FinanzasReales
        </p>
      </div>
      <div class="mt-4 flex md:mt-0 md:ml-4">
        <a 
          href="/" 
          target="_blank"
          class="ml-3 inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          Ver Sitio
        </a>
      </div>
    </div>

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Blog Stats -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Artículos del Blog
                </dt>
                <dd class="text-lg font-medium text-gray-900">
                  {stats.totalBlogPosts}
                </dd>
                <dd class="text-sm text-gray-500">
                  {stats.publishedBlogPosts} publicados, {stats.draftBlogPosts} borradores
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
          <div class="text-sm">
            <a href="/admin-finanzas-2024/blog" class="font-medium text-blue-600 hover:text-blue-500">
              Gestionar Blog
            </a>
          </div>
        </div>
      </div>

      <!-- Stories Stats -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Historias Financieras
                </dt>
                <dd class="text-lg font-medium text-gray-900">
                  {stats.totalStories}
                </dd>
                <dd class="text-sm text-gray-500">
                  {stats.publishedStories} publicadas, {stats.featuredStories} destacadas
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
          <div class="text-sm">
            <a href="/admin-finanzas-2024/stories" class="font-medium text-green-600 hover:text-green-500">
              Gestionar Historias
            </a>
          </div>
        </div>
      </div>

      <!-- Profiles Stats -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Perfiles Financieros
                </dt>
                <dd class="text-lg font-medium text-gray-900">
                  {stats.totalProfiles}
                </dd>
                <dd class="text-sm text-gray-500">
                  Situaciones financieras
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
          <div class="text-sm">
            <a href="/admin-finanzas-2024/profiles" class="font-medium text-purple-600 hover:text-purple-500">
              Gestionar Perfiles
            </a>
          </div>
        </div>
      </div>

      <!-- Resources Stats -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Recursos
                </dt>
                <dd class="text-lg font-medium text-gray-900">
                  {stats.totalResources}
                </dd>
                <dd class="text-sm text-gray-500">
                  Plantillas y guías
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
          <div class="text-sm">
            <a href="/admin-finanzas-2024/resources" class="font-medium text-orange-600 hover:text-orange-500">
              Gestionar Recursos
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg mb-8">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
          Acciones Rápidas
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <a 
            href="/admin-finanzas-2024/blog/new"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            Nuevo Artículo
          </a>
          <a 
            href="/admin-finanzas-2024/stories/new"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            Nueva Historia
          </a>
          <a 
            href="/admin-finanzas-2024/profiles/new"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            Nuevo Perfil
          </a>
          <a 
            href="/admin-finanzas-2024/resources/new"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            Nuevo Recurso
          </a>
        </div>
      </div>
    </div>

    <!-- Recent Content -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Recent Blog Posts -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            Artículos Recientes
          </h3>
          <div class="space-y-3">
            {recentBlogPosts.map(post => (
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <p class="text-sm font-medium text-gray-900 truncate">
                    {post.title}
                  </p>
                  <p class="text-sm text-gray-500">
                    {post.published ? 'Publicado' : 'Borrador'} • {new Date(post.updatedAt).toLocaleDateString('es-ES')}
                  </p>
                </div>
                <a 
                  href={`/admin-finanzas-2024/blog/edit/${post.id}`}
                  class="text-blue-600 hover:text-blue-500 text-sm"
                >
                  Editar
                </a>
              </div>
            ))}
          </div>
          <div class="mt-4">
            <a href="/admin-finanzas-2024/blog" class="text-sm text-blue-600 hover:text-blue-500">
              Ver todos los artículos →
            </a>
          </div>
        </div>
      </div>

      <!-- Recent Stories -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            Historias Recientes
          </h3>
          <div class="space-y-3">
            {recentStories.map(story => (
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <p class="text-sm font-medium text-gray-900 truncate">
                    {story.title}
                  </p>
                  <p class="text-sm text-gray-500">
                    {story.published ? 'Publicada' : 'Borrador'} • {new Date(story.updatedAt).toLocaleDateString('es-ES')}
                  </p>
                </div>
                <a 
                  href={`/admin-finanzas-2024/stories/edit/${story.id}`}
                  class="text-green-600 hover:text-green-500 text-sm"
                >
                  Editar
                </a>
              </div>
            ))}
          </div>
          <div class="mt-4">
            <a href="/admin-finanzas-2024/stories" class="text-sm text-green-600 hover:text-green-500">
              Ver todas las historias →
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
