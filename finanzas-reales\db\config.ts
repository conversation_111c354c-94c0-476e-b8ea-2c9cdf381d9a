import { defineDb, defineTable, column } from 'astro:db';

// Tabla de usuarios administradores
const User = defineTable({
  columns: {
    id: column.number({ primaryKey: true }),
    email: column.text({ unique: true }),
    password: column.text(),
    name: column.text(),
    role: column.text({ default: 'admin' }),
    createdAt: column.date({ default: new Date() }),
    lastLogin: column.date({ optional: true }),
  }
});

// Tabla de sesiones
const Session = defineTable({
  columns: {
    id: column.text({ primaryKey: true }),
    userId: column.number({ references: () => User.columns.id }),
    expiresAt: column.date(),
    createdAt: column.date({ default: new Date() }),
  }
});

// Tabla de artículos del blog
const BlogPost = defineTable({
  columns: {
    id: column.number({ primaryKey: true }),
    slug: column.text({ unique: true }),
    title: column.text(),
    excerpt: column.text(),
    content: column.text(),
    category: column.text(),
    tags: column.text(), // JSON string
    readTime: column.text(),
    featured: column.boolean({ default: false }),
    published: column.boolean({ default: false }),
    publishDate: column.date({ optional: true }),
    createdAt: column.date({ default: new Date() }),
    updatedAt: column.date({ default: new Date() }),
    authorId: column.number({ references: () => User.columns.id }),
  }
});

// Tabla de perfiles financieros
const FinancialProfile = defineTable({
  columns: {
    id: column.number({ primaryKey: true }),
    profileId: column.text({ unique: true }),
    title: column.text(),
    subtitle: column.text(),
    description: column.text(),
    icon: column.text(),
    color: column.text(),
    situation: column.text(),
    challenges: column.text(), // JSON string
    solutions: column.text(), // JSON string
    stats: column.text(), // JSON string
    featuredStory: column.text(), // JSON string
    resources: column.text(), // JSON string
    published: column.boolean({ default: true }),
    createdAt: column.date({ default: new Date() }),
    updatedAt: column.date({ default: new Date() }),
  }
});

// Tabla de historias financieras
const FinancialStory = defineTable({
  columns: {
    id: column.number({ primaryKey: true }),
    storyId: column.text({ unique: true }),
    title: column.text(),
    subtitle: column.text(),
    profileId: column.text(),
    person: column.text(), // JSON string
    situation: column.text(), // JSON string
    strategy: column.text(), // JSON string
    results: column.text(), // JSON string
    resources: column.text(), // JSON string
    updates: column.text(), // JSON string
    tags: column.text(), // JSON string
    featured: column.boolean({ default: false }),
    published: column.boolean({ default: false }),
    publishDate: column.date({ optional: true }),
    lastUpdate: column.date({ default: new Date() }),
    createdAt: column.date({ default: new Date() }),
    updatedAt: column.date({ default: new Date() }),
    authorId: column.number({ references: () => User.columns.id }),
  }
});

// Tabla de recursos (plantillas, guías, etc.)
const Resource = defineTable({
  columns: {
    id: column.number({ primaryKey: true }),
    slug: column.text({ unique: true }),
    title: column.text(),
    description: column.text(),
    type: column.text(), // 'template', 'guide', 'tool'
    category: column.text(),
    filePath: column.text({ optional: true }),
    downloadUrl: column.text({ optional: true }),
    externalUrl: column.text({ optional: true }),
    basedOnStory: column.text({ optional: true }),
    tags: column.text(), // JSON string
    downloadCount: column.number({ default: 0 }),
    published: column.boolean({ default: true }),
    createdAt: column.date({ default: new Date() }),
    updatedAt: column.date({ default: new Date() }),
    authorId: column.number({ references: () => User.columns.id }),
  }
});

// Tabla de estadísticas del sitio
const SiteStats = defineTable({
  columns: {
    id: column.number({ primaryKey: true }),
    date: column.date(),
    pageViews: column.number({ default: 0 }),
    uniqueVisitors: column.number({ default: 0 }),
    downloads: column.number({ default: 0 }),
    newsletterSignups: column.number({ default: 0 }),
    topPages: column.text(), // JSON string
    topResources: column.text(), // JSON string
  }
});

// Tabla de configuración del sitio
const SiteConfig = defineTable({
  columns: {
    id: column.number({ primaryKey: true }),
    key: column.text({ unique: true }),
    value: column.text(),
    description: column.text({ optional: true }),
    updatedAt: column.date({ default: new Date() }),
    updatedBy: column.number({ references: () => User.columns.id }),
  }
});

export default defineDb({
  tables: {
    User,
    Session,
    BlogPost,
    FinancialProfile,
    FinancialStory,
    Resource,
    SiteStats,
    SiteConfig,
  }
});
