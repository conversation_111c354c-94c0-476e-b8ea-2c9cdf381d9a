---
import Layout from '../../layouts/Layout.astro';
---

<Layout title="Descarga: Presupuesto para Ingresos Variables - FinanzasReales">
	<section class="bg-white py-16">
		<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center mb-8">
				<h1 class="text-4xl font-bold text-gray-900 mb-4">
					Presupuesto para Ingresos Variables
				</h1>
				<p class="text-xl text-gray-600 mb-8">
					El sistema que Carlos desarrolló para manejar ingresos de €2K-€8K mensuales
				</p>
			</div>

			<div class="bg-green-50 border border-green-200 rounded-xl p-8 mb-8">
				<h2 class="text-2xl font-bold text-green-900 mb-4">¿Qué incluye esta plantilla?</h2>
				<ul class="space-y-3 text-green-800">
					<li class="flex items-start">
						<svg class="w-5 h-5 text-green-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
						</svg>
						Sistema de presupuesto por percentiles
					</li>
					<li class="flex items-start">
						<svg class="w-5 h-5 text-green-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
						</svg>
						Fórmulas de ahorro adaptativo automático
					</li>
					<li class="flex items-start">
						<svg class="w-5 h-5 text-green-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
						</svg>
						Calculadora de fondo de estabilización
					</li>
					<li class="flex items-start">
						<svg class="w-5 h-5 text-green-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
						</svg>
						Dashboard de seguimiento mensual
					</li>
				</ul>
			</div>

			<div class="bg-purple-50 border border-purple-200 rounded-xl p-8 mb-8">
				<h3 class="text-xl font-bold text-purple-900 mb-3">Historia de Éxito: Carlos</h3>
				<p class="text-purple-800 mb-4">
					Carlos usó este sistema para estabilizar sus finanzas con ingresos que variaban entre €2,000 y €8,000 mensuales. 
					Ahora tiene un fondo de €18,000 e invierte €400/mes consistentemente.
				</p>
				<a href="/historias/carlos-ingresos-variables" class="text-purple-600 hover:text-purple-700 font-medium">
					Leer la historia completa de Carlos →
				</a>
			</div>

			<form class="bg-gray-50 rounded-xl p-8" id="download-form">
				<h3 class="text-xl font-bold text-gray-900 mb-6">Descarga Gratuita</h3>
				
				<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
					<div>
						<label for="name" class="block text-sm font-medium text-gray-700 mb-2">
							Nombre *
						</label>
						<input 
							type="text" 
							id="name" 
							name="name" 
							required
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
							placeholder="Tu nombre"
						>
					</div>
					
					<div>
						<label for="email" class="block text-sm font-medium text-gray-700 mb-2">
							Email *
						</label>
						<input 
							type="email" 
							id="email" 
							name="email" 
							required
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
							placeholder="<EMAIL>"
						>
					</div>
				</div>

				<div class="mb-6">
					<label for="income-type" class="block text-sm font-medium text-gray-700 mb-2">
						¿Cuál describe mejor tu situación? (Opcional)
					</label>
					<select 
						id="income-type" 
						name="income-type"
						class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
					>
						<option value="">Selecciona una opción</option>
						<option value="freelancer">Freelancer/Consultor</option>
						<option value="business">Pequeño negocio</option>
						<option value="commission">Ventas por comisión</option>
						<option value="seasonal">Negocio estacional</option>
						<option value="startup">Startup/Nuevo negocio</option>
						<option value="other">Otro</option>
					</select>
				</div>
				
				<div class="flex items-start mb-6">
					<input 
						type="checkbox" 
						id="newsletter" 
						name="newsletter"
						class="mt-1 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
					>
					<label for="newsletter" class="ml-2 text-sm text-gray-700">
						Quiero recibir consejos para emprendedores y nuevos recursos por email (opcional)
					</label>
				</div>
				
				<div class="flex items-start mb-6">
					<input 
						type="checkbox" 
						id="privacy" 
						name="privacy" 
						required
						class="mt-1 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
					>
					<label for="privacy" class="ml-2 text-sm text-gray-700">
						Acepto la <a href="/privacidad" class="text-green-600 hover:text-green-700">política de privacidad</a> *
					</label>
				</div>
				
				<button 
					type="submit"
					class="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
				>
					Descargar Plantilla Excel Gratis
				</button>
			</form>
		</div>
	</section>
</Layout>

<script>
	document.getElementById('download-form')?.addEventListener('submit', function(e) {
		e.preventDefault();
		
		const button = this.querySelector('button[type="submit"]');
		const originalText = button.textContent;
		
		button.textContent = 'Preparando descarga...';
		button.disabled = true;
		
		setTimeout(() => {
			button.textContent = '✓ ¡Descarga iniciada!';
			button.classList.remove('bg-green-600', 'hover:bg-green-700');
			button.classList.add('bg-blue-600');
			
			// Create a mock download link
			const link = document.createElement('a');
			link.href = '/assets/plantillas/presupuesto-ingresos-variables-carlos.xlsx';
			link.download = 'Presupuesto-Ingresos-Variables-FinanzasReales.xlsx';
			link.click();
			
			setTimeout(() => {
				button.textContent = originalText;
				button.disabled = false;
				button.classList.remove('bg-blue-600');
				button.classList.add('bg-green-600', 'hover:bg-green-700');
			}, 3000);
		}, 2000);
	});
</script>
