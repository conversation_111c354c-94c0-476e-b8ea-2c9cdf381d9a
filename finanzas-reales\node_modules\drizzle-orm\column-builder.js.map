{"version": 3, "sources": ["../src/column-builder.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { Column } from './column.ts';\nimport type { GelColumn, GelExtraConfigColumn } from './gel-core/index.ts';\nimport type { MySqlColumn } from './mysql-core/index.ts';\nimport type { ExtraConfigColumn, PgColumn, PgSequenceOptions } from './pg-core/index.ts';\nimport type { SingleStoreColumn } from './singlestore-core/index.ts';\nimport type { SQL } from './sql/sql.ts';\nimport type { SQLiteColumn } from './sqlite-core/index.ts';\nimport type { Assume, Simplify } from './utils.ts';\n\nexport type ColumnDataType =\n\t| 'string'\n\t| 'number'\n\t| 'boolean'\n\t| 'array'\n\t| 'json'\n\t| 'date'\n\t| 'bigint'\n\t| 'custom'\n\t| 'buffer'\n\t| 'dateDuration'\n\t| 'duration'\n\t| 'relDuration'\n\t| 'localTime'\n\t| 'localDate'\n\t| 'localDateTime';\n\nexport type Dialect = 'pg' | 'mysql' | 'sqlite' | 'singlestore' | 'common' | 'gel';\n\nexport type GeneratedStorageMode = 'virtual' | 'stored';\n\nexport type GeneratedType = 'always' | 'byDefault';\n\nexport type GeneratedColumnConfig<TDataType> = {\n\tas: TDataType | SQL | (() => SQL);\n\ttype?: GeneratedType;\n\tmode?: GeneratedStorageMode;\n};\n\nexport type GeneratedIdentityConfig = {\n\tsequenceName?: string;\n\tsequenceOptions?: PgSequenceOptions;\n\ttype: 'always' | 'byDefault';\n};\n\nexport interface ColumnBuilderBaseConfig<TDataType extends ColumnDataType, TColumnType extends string> {\n\tname: string;\n\tdataType: TDataType;\n\tcolumnType: TColumnType;\n\tdata: unknown;\n\tdriverParam: unknown;\n\tenumValues: string[] | undefined;\n}\n\nexport type MakeColumnConfig<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTableName extends string,\n\tTData = T extends { $type: infer U } ? U : T['data'],\n> = {\n\tname: T['name'];\n\ttableName: TTableName;\n\tdataType: T['dataType'];\n\tcolumnType: T['columnType'];\n\tdata: TData;\n\tdriverParam: T['driverParam'];\n\tnotNull: T extends { notNull: true } ? true : false;\n\thasDefault: T extends { hasDefault: true } ? true : false;\n\tisPrimaryKey: T extends { isPrimaryKey: true } ? true : false;\n\tisAutoincrement: T extends { isAutoincrement: true } ? true : false;\n\thasRuntimeDefault: T extends { hasRuntimeDefault: true } ? true : false;\n\tenumValues: T['enumValues'];\n\tbaseColumn: T extends { baseBuilder: infer U extends ColumnBuilderBase } ? BuildColumn<TTableName, U, 'common'>\n\t\t: never;\n\tidentity: T extends { identity: 'always' } ? 'always' : T extends { identity: 'byDefault' } ? 'byDefault' : undefined;\n\tgenerated: T extends { generated: infer G } ? unknown extends G ? undefined\n\t\t: G extends undefined ? undefined\n\t\t: G\n\t\t: undefined;\n} & {};\n\nexport type ColumnBuilderTypeConfig<\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTypeConfig extends object = object,\n> = Simplify<\n\t& {\n\t\tbrand: 'ColumnBuilder';\n\t\tname: T['name'];\n\t\tdataType: T['dataType'];\n\t\tcolumnType: T['columnType'];\n\t\tdata: T['data'];\n\t\tdriverParam: T['driverParam'];\n\t\tnotNull: T extends { notNull: infer U } ? U : boolean;\n\t\thasDefault: T extends { hasDefault: infer U } ? U : boolean;\n\t\tenumValues: T['enumValues'];\n\t\tidentity: T extends { identity: infer U } ? U : unknown;\n\t\tgenerated: T extends { generated: infer G } ? G extends undefined ? unknown : G : unknown;\n\t}\n\t& TTypeConfig\n>;\n\nexport type ColumnBuilderRuntimeConfig<TData, TRuntimeConfig extends object = object> = {\n\tname: string;\n\tkeyAsName: boolean;\n\tnotNull: boolean;\n\tdefault: TData | SQL | undefined;\n\tdefaultFn: (() => TData | SQL) | undefined;\n\tonUpdateFn: (() => TData | SQL) | undefined;\n\thasDefault: boolean;\n\tprimaryKey: boolean;\n\tisUnique: boolean;\n\tuniqueName: string | undefined;\n\tuniqueType: string | undefined;\n\tdataType: string;\n\tcolumnType: string;\n\tgenerated: GeneratedColumnConfig<TData> | undefined;\n\tgeneratedIdentity: GeneratedIdentityConfig | undefined;\n} & TRuntimeConfig;\n\nexport interface ColumnBuilderExtraConfig {\n\tprimaryKeyHasDefault?: boolean;\n}\n\nexport type NotNull<T extends ColumnBuilderBase> = T & {\n\t_: {\n\t\tnotNull: true;\n\t};\n};\n\nexport type HasDefault<T extends ColumnBuilderBase> = T & {\n\t_: {\n\t\thasDefault: true;\n\t};\n};\n\nexport type IsPrimaryKey<T extends ColumnBuilderBase> = T & {\n\t_: {\n\t\tisPrimaryKey: true;\n\t};\n};\n\nexport type IsAutoincrement<T extends ColumnBuilderBase> = T & {\n\t_: {\n\t\tisAutoincrement: true;\n\t};\n};\n\nexport type HasRuntimeDefault<T extends ColumnBuilderBase> = T & {\n\t_: {\n\t\thasRuntimeDefault: true;\n\t};\n};\n\nexport type $Type<T extends ColumnBuilderBase, TType> = T & {\n\t_: {\n\t\t$type: TType;\n\t};\n};\n\nexport type HasGenerated<T extends ColumnBuilderBase, TGenerated extends {} = {}> = T & {\n\t_: {\n\t\thasDefault: true;\n\t\tgenerated: TGenerated;\n\t};\n};\n\nexport type IsIdentity<\n\tT extends ColumnBuilderBase,\n\tTType extends 'always' | 'byDefault',\n> = T & {\n\t_: {\n\t\tnotNull: true;\n\t\thasDefault: true;\n\t\tidentity: TType;\n\t};\n};\nexport interface ColumnBuilderBase<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTypeConfig extends object = object,\n> {\n\t_: ColumnBuilderTypeConfig<T, TTypeConfig>;\n}\n\n// To understand how to use `ColumnBuilder` and `AnyColumnBuilder`, see `Column` and `AnyColumn` documentation.\nexport abstract class ColumnBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n\tTTypeConfig extends object = object,\n\tTExtraConfig extends ColumnBuilderExtraConfig = ColumnBuilderExtraConfig,\n> implements ColumnBuilderBase<T, TTypeConfig> {\n\tstatic readonly [entityKind]: string = 'ColumnBuilder';\n\n\tdeclare _: ColumnBuilderTypeConfig<T, TTypeConfig>;\n\n\tprotected config: ColumnBuilderRuntimeConfig<T['data'], TRuntimeConfig>;\n\n\tconstructor(name: T['name'], dataType: T['dataType'], columnType: T['columnType']) {\n\t\tthis.config = {\n\t\t\tname,\n\t\t\tkeyAsName: name === '',\n\t\t\tnotNull: false,\n\t\t\tdefault: undefined,\n\t\t\thasDefault: false,\n\t\t\tprimaryKey: false,\n\t\t\tisUnique: false,\n\t\t\tuniqueName: undefined,\n\t\t\tuniqueType: undefined,\n\t\t\tdataType,\n\t\t\tcolumnType,\n\t\t\tgenerated: undefined,\n\t\t} as ColumnBuilderRuntimeConfig<T['data'], TRuntimeConfig>;\n\t}\n\n\t/**\n\t * Changes the data type of the column. Commonly used with `json` columns. Also, useful for branded types.\n\t *\n\t * @example\n\t * ```ts\n\t * const users = pgTable('users', {\n\t * \tid: integer('id').$type<UserId>().primaryKey(),\n\t * \tdetails: json('details').$type<UserDetails>().notNull(),\n\t * });\n\t * ```\n\t */\n\t$type<TType>(): $Type<this, TType> {\n\t\treturn this as $Type<this, TType>;\n\t}\n\n\t/**\n\t * Adds a `not null` clause to the column definition.\n\t *\n\t * Affects the `select` model of the table - columns *without* `not null` will be nullable on select.\n\t */\n\tnotNull(): NotNull<this> {\n\t\tthis.config.notNull = true;\n\t\treturn this as NotNull<this>;\n\t}\n\n\t/**\n\t * Adds a `default <value>` clause to the column definition.\n\t *\n\t * Affects the `insert` model of the table - columns *with* `default` are optional on insert.\n\t *\n\t * If you need to set a dynamic default value, use {@link $defaultFn} instead.\n\t */\n\tdefault(value: (this['_'] extends { $type: infer U } ? U : this['_']['data']) | SQL): HasDefault<this> {\n\t\tthis.config.default = value;\n\t\tthis.config.hasDefault = true;\n\t\treturn this as HasDefault<this>;\n\t}\n\n\t/**\n\t * Adds a dynamic default value to the column.\n\t * The function will be called when the row is inserted, and the returned value will be used as the column value.\n\t *\n\t * **Note:** This value does not affect the `drizzle-kit` behavior, it is only used at runtime in `drizzle-orm`.\n\t */\n\t$defaultFn(\n\t\tfn: () => (this['_'] extends { $type: infer U } ? U : this['_']['data']) | SQL,\n\t): HasRuntimeDefault<HasDefault<this>> {\n\t\tthis.config.defaultFn = fn;\n\t\tthis.config.hasDefault = true;\n\t\treturn this as HasRuntimeDefault<HasDefault<this>>;\n\t}\n\n\t/**\n\t * Alias for {@link $defaultFn}.\n\t */\n\t$default = this.$defaultFn;\n\n\t/**\n\t * Adds a dynamic update value to the column.\n\t * The function will be called when the row is updated, and the returned value will be used as the column value if none is provided.\n\t * If no `default` (or `$defaultFn`) value is provided, the function will be called when the row is inserted as well, and the returned value will be used as the column value.\n\t *\n\t * **Note:** This value does not affect the `drizzle-kit` behavior, it is only used at runtime in `drizzle-orm`.\n\t */\n\t$onUpdateFn(\n\t\tfn: () => (this['_'] extends { $type: infer U } ? U : this['_']['data']) | SQL,\n\t): HasDefault<this> {\n\t\tthis.config.onUpdateFn = fn;\n\t\tthis.config.hasDefault = true;\n\t\treturn this as HasDefault<this>;\n\t}\n\n\t/**\n\t * Alias for {@link $onUpdateFn}.\n\t */\n\t$onUpdate = this.$onUpdateFn;\n\n\t/**\n\t * Adds a `primary key` clause to the column definition. This implicitly makes the column `not null`.\n\t *\n\t * In SQLite, `integer primary key` implicitly makes the column auto-incrementing.\n\t */\n\tprimaryKey(): TExtraConfig['primaryKeyHasDefault'] extends true ? IsPrimaryKey<HasDefault<NotNull<this>>>\n\t\t: IsPrimaryKey<NotNull<this>>\n\t{\n\t\tthis.config.primaryKey = true;\n\t\tthis.config.notNull = true;\n\t\treturn this as TExtraConfig['primaryKeyHasDefault'] extends true ? IsPrimaryKey<HasDefault<NotNull<this>>>\n\t\t\t: IsPrimaryKey<NotNull<this>>;\n\t}\n\n\tabstract generatedAlwaysAs(\n\t\tas: SQL | T['data'] | (() => SQL),\n\t\tconfig?: Partial<GeneratedColumnConfig<unknown>>,\n\t): HasGenerated<this, {\n\t\ttype: 'always';\n\t}>;\n\n\t/** @internal Sets the name of the column to the key within the table definition if a name was not given. */\n\tsetName(name: string) {\n\t\tif (this.config.name !== '') return;\n\t\tthis.config.name = name;\n\t}\n}\n\nexport type BuildColumn<\n\tTTableName extends string,\n\tTBuilder extends ColumnBuilderBase,\n\tTDialect extends Dialect,\n> = TDialect extends 'pg' ? PgColumn<\n\t\tMakeColumnConfig<TBuilder['_'], TTableName>,\n\t\t{},\n\t\tSimplify<Omit<TBuilder['_'], keyof MakeColumnConfig<TBuilder['_'], TTableName> | 'brand' | 'dialect'>>\n\t>\n\t: TDialect extends 'mysql' ? MySqlColumn<\n\t\t\tMakeColumnConfig<TBuilder['_'], TTableName>,\n\t\t\t{},\n\t\t\tSimplify<\n\t\t\t\tOmit<\n\t\t\t\t\tTBuilder['_'],\n\t\t\t\t\t| keyof MakeColumnConfig<TBuilder['_'], TTableName>\n\t\t\t\t\t| 'brand'\n\t\t\t\t\t| 'dialect'\n\t\t\t\t\t| 'primaryKeyHasDefault'\n\t\t\t\t\t| 'mysqlColumnBuilderBrand'\n\t\t\t\t>\n\t\t\t>\n\t\t>\n\t: TDialect extends 'sqlite' ? SQLiteColumn<\n\t\t\tMakeColumnConfig<TBuilder['_'], TTableName>,\n\t\t\t{},\n\t\t\tSimplify<Omit<TBuilder['_'], keyof MakeColumnConfig<TBuilder['_'], TTableName> | 'brand' | 'dialect'>>\n\t\t>\n\t: TDialect extends 'common' ? Column<\n\t\t\tMakeColumnConfig<TBuilder['_'], TTableName>,\n\t\t\t{},\n\t\t\tSimplify<Omit<TBuilder['_'], keyof MakeColumnConfig<TBuilder['_'], TTableName> | 'brand' | 'dialect'>>\n\t\t>\n\t: TDialect extends 'singlestore' ? SingleStoreColumn<\n\t\t\tMakeColumnConfig<TBuilder['_'], TTableName>,\n\t\t\t{},\n\t\t\tSimplify<\n\t\t\t\tOmit<\n\t\t\t\t\tTBuilder['_'],\n\t\t\t\t\t| keyof MakeColumnConfig<TBuilder['_'], TTableName>\n\t\t\t\t\t| 'brand'\n\t\t\t\t\t| 'dialect'\n\t\t\t\t\t| 'primaryKeyHasDefault'\n\t\t\t\t\t| 'singlestoreColumnBuilderBrand'\n\t\t\t\t>\n\t\t\t>\n\t\t>\n\t: TDialect extends 'gel' ? GelColumn<\n\t\t\tMakeColumnConfig<TBuilder['_'], TTableName>,\n\t\t\t{},\n\t\t\tSimplify<Omit<TBuilder['_'], keyof MakeColumnConfig<TBuilder['_'], TTableName> | 'brand' | 'dialect'>>\n\t\t>\n\t: never;\n\nexport type BuildIndexColumn<\n\tTDialect extends Dialect,\n> = TDialect extends 'pg' ? ExtraConfigColumn\n\t: TDialect extends 'gel' ? GelExtraConfigColumn\n\t: never;\n\n// TODO\n// try to make sql as well + indexRaw\n\n// optional after everything will be working as expected\n// also try to leave only needed methods for extraConfig\n// make an error if I pass .asc() to fk and so on\n\nexport type BuildColumns<\n\tTTableName extends string,\n\tTConfigMap extends Record<string, ColumnBuilderBase>,\n\tTDialect extends Dialect,\n> =\n\t& {\n\t\t[Key in keyof TConfigMap]: BuildColumn<TTableName, {\n\t\t\t_:\n\t\t\t\t& Omit<TConfigMap[Key]['_'], 'name'>\n\t\t\t\t& { name: TConfigMap[Key]['_']['name'] extends '' ? Assume<Key, string> : TConfigMap[Key]['_']['name'] };\n\t\t}, TDialect>;\n\t}\n\t& {};\n\nexport type BuildExtraConfigColumns<\n\t_TTableName extends string,\n\tTConfigMap extends Record<string, ColumnBuilderBase>,\n\tTDialect extends Dialect,\n> =\n\t& {\n\t\t[Key in keyof TConfigMap]: BuildIndexColumn<TDialect>;\n\t}\n\t& {};\n\nexport type ChangeColumnTableName<TColumn extends Column, TAlias extends string, TDialect extends Dialect> =\n\tTDialect extends 'pg' ? PgColumn<MakeColumnConfig<TColumn['_'], TAlias>>\n\t\t: TDialect extends 'mysql' ? MySqlColumn<MakeColumnConfig<TColumn['_'], TAlias>>\n\t\t: TDialect extends 'singlestore' ? SingleStoreColumn<MakeColumnConfig<TColumn['_'], TAlias>>\n\t\t: TDialect extends 'sqlite' ? SQLiteColumn<MakeColumnConfig<TColumn['_'], TAlias>>\n\t\t: TDialect extends 'gel' ? GelColumn<MakeColumnConfig<TColumn['_'], TAlias>>\n\t\t: never;\n"], "mappings": "AAAA,SAAS,kBAAkB;AAwLpB,MAAe,cAKyB;AAAA,EAC9C,QAAiB,UAAU,IAAY;AAAA,EAI7B;AAAA,EAEV,YAAY,MAAiB,UAAyB,YAA6B;AAClF,SAAK,SAAS;AAAA,MACb;AAAA,MACA,WAAW,SAAS;AAAA,MACpB,SAAS;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA,WAAW;AAAA,IACZ;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,QAAmC;AAClC,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAyB;AACxB,SAAK,OAAO,UAAU;AACtB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,OAA+F;AACtG,SAAK,OAAO,UAAU;AACtB,SAAK,OAAO,aAAa;AACzB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WACC,IACsC;AACtC,SAAK,OAAO,YAAY;AACxB,SAAK,OAAO,aAAa;AACzB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAShB,YACC,IACmB;AACnB,SAAK,OAAO,aAAa;AACzB,SAAK,OAAO,aAAa;AACzB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjB,aAEA;AACC,SAAK,OAAO,aAAa;AACzB,SAAK,OAAO,UAAU;AACtB,WAAO;AAAA,EAER;AAAA;AAAA,EAUA,QAAQ,MAAc;AACrB,QAAI,KAAK,OAAO,SAAS;AAAI;AAC7B,SAAK,OAAO,OAAO;AAAA,EACpB;AACD;", "names": []}