---
import { verifySession } from '../../lib/auth';
import AdminLayout from '../../layouts/AdminLayout.astro';

// Verificar autenticación
const authToken = Astro.cookies.get('auth-token')?.value;
if (!authToken) {
  return Astro.redirect('/admin-finanzas-2024/login');
}

const session = await verifySession(authToken);
if (!session) {
  Astro.cookies.delete('auth-token');
  return Astro.redirect('/admin-finanzas-2024/login');
}
---

<AdminLayout title="Gestión de Perfiles - Admin FinanzasReales">
  <!-- Navigation -->
  <nav class="admin-nav">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center space-x-4">
          <a href="/admin-finanzas-2024/dashboard" class="text-xl font-semibold text-gray-900">
            FinanzasReales Admin
          </a>
          <span class="text-gray-400">|</span>
          <span class="text-gray-600">Perfiles</span>
        </div>
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-700">
            {session.name}
          </span>
          <a 
            href="/admin-finanzas-2024/logout" 
            class="text-sm text-red-600 hover:text-red-800"
          >
            Cerrar Sesión
          </a>
        </div>
      </div>
    </div>
  </nav>

  <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
    <!-- Page header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">
        Gestión de Perfiles Financieros
      </h1>
      <p class="text-gray-600 text-lg">
        Administra los perfiles de situaciones financieras
      </p>
    </div>

    <!-- Coming Soon Message -->
    <div class="bg-white rounded-xl shadow-lg p-8 text-center">
      <div class="max-w-md mx-auto">
        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
          </svg>
        </div>
        <h2 class="text-xl font-bold text-gray-900 mb-2">
          Gestión de Perfiles
        </h2>
        <p class="text-gray-600 mb-6">
          Esta funcionalidad está en desarrollo. Pronto podrás gestionar todos los perfiles financieros desde aquí.
        </p>
        <div class="space-y-3">
          <p class="text-sm text-gray-500">
            <strong>Funcionalidades próximas:</strong>
          </p>
          <ul class="text-sm text-gray-500 space-y-1">
            <li>• Crear nuevos perfiles financieros</li>
            <li>• Editar perfiles existentes</li>
            <li>• Gestionar desafíos y soluciones</li>
            <li>• Configurar estadísticas y recursos</li>
          </ul>
        </div>
        <div class="mt-6">
          <a 
            href="/admin-finanzas-2024/dashboard"
            class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Volver al Dashboard
          </a>
        </div>
      </div>
    </div>
  </div>
</AdminLayout>
