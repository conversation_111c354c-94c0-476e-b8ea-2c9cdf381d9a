import { entityKind } from "../entity.js";
import { DefaultLogger } from "../logger.js";
import {
  createTableRelationsHelpers,
  extractTablesRelationalConfig
} from "../relations.js";
import { BaseSQLiteDatabase } from "../sqlite-core/db.js";
import { SQLiteAsyncDialect } from "../sqlite-core/dialect.js";
import { OPSQLiteSession } from "./session.js";
class OPSQLiteDatabase extends BaseSQLiteDatabase {
  static [entityKind] = "OPSQLiteDatabase";
}
function drizzle(client, config = {}) {
  const dialect = new SQLiteAsyncDialect({ casing: config.casing });
  let logger;
  if (config.logger === true) {
    logger = new DefaultLogger();
  } else if (config.logger !== false) {
    logger = config.logger;
  }
  let schema;
  if (config.schema) {
    const tablesConfig = extractTablesRelationalConfig(
      config.schema,
      createTableRelationsHelpers
    );
    schema = {
      fullSchema: config.schema,
      schema: tablesConfig.tables,
      tableNamesMap: tablesConfig.tableNamesMap
    };
  }
  const session = new OPSQLiteSession(client, dialect, schema, { logger });
  const db = new OPSQLiteDatabase("async", dialect, session, schema);
  db.$client = client;
  return db;
}
export {
  OPSQLiteDatabase,
  drizzle
};
//# sourceMappingURL=driver.js.map