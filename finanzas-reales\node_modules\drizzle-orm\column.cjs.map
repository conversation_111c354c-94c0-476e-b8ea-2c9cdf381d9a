{"version": 3, "sources": ["../src/column.ts"], "sourcesContent": ["import type {\n\tColumnBuilderBaseConfig,\n\tColumnBuilderRuntimeConfig,\n\tColumnDataType,\n\tGeneratedColumnConfig,\n\tGeneratedIdentityConfig,\n} from './column-builder.ts';\nimport { entityKind } from './entity.ts';\nimport type { DriverValueMapper, SQL, SQLWrapper } from './sql/sql.ts';\nimport type { Table } from './table.ts';\nimport type { Update } from './utils.ts';\n\nexport interface ColumnBaseConfig<\n\tTDataType extends ColumnDataType,\n\tTColumnType extends string,\n> extends ColumnBuilderBaseConfig<TDataType, TColumnType> {\n\ttableName: string;\n\tnotNull: boolean;\n\thasDefault: boolean;\n\tisPrimaryKey: boolean;\n\tisAutoincrement: boolean;\n\thasRuntimeDefault: boolean;\n}\n\nexport type ColumnTypeConfig<T extends ColumnBaseConfig<ColumnDataType, string>, TTypeConfig extends object> = T & {\n\tbrand: 'Column';\n\ttableName: T['tableName'];\n\tname: T['name'];\n\tdataType: T['dataType'];\n\tcolumnType: T['columnType'];\n\tdata: T['data'];\n\tdriverParam: T['driverParam'];\n\tnotNull: T['notNull'];\n\thasDefault: T['hasDefault'];\n\tisPrimaryKey: T['isPrimaryKey'];\n\tisAutoincrement: T['isAutoincrement'];\n\thasRuntimeDefault: T['hasRuntimeDefault'];\n\tenumValues: T['enumValues'];\n\tbaseColumn: T extends { baseColumn: infer U } ? U : unknown;\n\tgenerated: GeneratedColumnConfig<T['data']> | undefined;\n\tidentity: undefined | 'always' | 'byDefault';\n} & TTypeConfig;\n\nexport type ColumnRuntimeConfig<TData, TRuntimeConfig extends object> = ColumnBuilderRuntimeConfig<\n\tTData,\n\tTRuntimeConfig\n>;\n\nexport interface Column<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTRuntimeConfig extends object = object,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTTypeConfig extends object = object,\n> extends DriverValueMapper<T['data'], T['driverParam']>, SQLWrapper {\n\t// SQLWrapper runtime implementation is defined in 'sql/sql.ts'\n}\n/*\n\t`Column` only accepts a full `ColumnConfig` as its generic.\n\tTo infer parts of the config, use `AnyColumn` that accepts a partial config.\n\tSee `GetColumnData` for example usage of inferring.\n*/\nexport abstract class Column<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n\tTTypeConfig extends object = object,\n> implements DriverValueMapper<T['data'], T['driverParam']>, SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Column';\n\n\tdeclare readonly _: ColumnTypeConfig<T, TTypeConfig>;\n\n\treadonly name: string;\n\treadonly keyAsName: boolean;\n\treadonly primary: boolean;\n\treadonly notNull: boolean;\n\treadonly default: T['data'] | SQL | undefined;\n\treadonly defaultFn: (() => T['data'] | SQL) | undefined;\n\treadonly onUpdateFn: (() => T['data'] | SQL) | undefined;\n\treadonly hasDefault: boolean;\n\treadonly isUnique: boolean;\n\treadonly uniqueName: string | undefined;\n\treadonly uniqueType: string | undefined;\n\treadonly dataType: T['dataType'];\n\treadonly columnType: T['columnType'];\n\treadonly enumValues: T['enumValues'] = undefined;\n\treadonly generated: GeneratedColumnConfig<T['data']> | undefined = undefined;\n\treadonly generatedIdentity: GeneratedIdentityConfig | undefined = undefined;\n\n\tprotected config: ColumnRuntimeConfig<T['data'], TRuntimeConfig>;\n\n\tconstructor(\n\t\treadonly table: Table,\n\t\tconfig: ColumnRuntimeConfig<T['data'], TRuntimeConfig>,\n\t) {\n\t\tthis.config = config;\n\t\tthis.name = config.name;\n\t\tthis.keyAsName = config.keyAsName;\n\t\tthis.notNull = config.notNull;\n\t\tthis.default = config.default;\n\t\tthis.defaultFn = config.defaultFn;\n\t\tthis.onUpdateFn = config.onUpdateFn;\n\t\tthis.hasDefault = config.hasDefault;\n\t\tthis.primary = config.primaryKey;\n\t\tthis.isUnique = config.isUnique;\n\t\tthis.uniqueName = config.uniqueName;\n\t\tthis.uniqueType = config.uniqueType;\n\t\tthis.dataType = config.dataType as T['dataType'];\n\t\tthis.columnType = config.columnType;\n\t\tthis.generated = config.generated;\n\t\tthis.generatedIdentity = config.generatedIdentity;\n\t}\n\n\tabstract getSQLType(): string;\n\n\tmapFromDriverValue(value: unknown): unknown {\n\t\treturn value;\n\t}\n\n\tmapToDriverValue(value: unknown): unknown {\n\t\treturn value;\n\t}\n\n\t// ** @internal */\n\tshouldDisableInsert(): boolean {\n\t\treturn this.config.generated !== undefined && this.config.generated.type !== 'byDefault';\n\t}\n}\n\nexport type UpdateColConfig<\n\tT extends ColumnBaseConfig<ColumnDataType, string>,\n\tTUpdate extends Partial<ColumnBaseConfig<ColumnDataType, string>>,\n> = Update<T, TUpdate>;\n\nexport type AnyColumn<TPartial extends Partial<ColumnBaseConfig<ColumnDataType, string>> = {}> = Column<\n\tRequired<Update<ColumnBaseConfig<ColumnDataType, string>, TPartial>>\n>;\n\nexport type GetColumnData<TColumn extends Column, TInferMode extends 'query' | 'raw' = 'query'> =\n\t// dprint-ignore\n\tTInferMode extends 'raw' // Raw mode\n\t\t? TColumn['_']['data'] // Just return the underlying type\n\t\t: TColumn['_']['notNull'] extends true // Query mode\n\t\t? TColumn['_']['data'] // Query mode, not null\n\t\t: TColumn['_']['data'] | null; // Query mode, nullable\n\nexport type InferColumnsDataTypes<TColumns extends Record<string, Column>> = {\n\t[Key in keyof TColumns]: GetColumnData<TColumns[Key], 'query'>;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,oBAA2B;AAuDpB,MAAe,OAIkD;AAAA,EAwBvE,YACU,OACT,QACC;AAFQ;AAGT,SAAK,SAAS;AACd,SAAK,OAAO,OAAO;AACnB,SAAK,YAAY,OAAO;AACxB,SAAK,UAAU,OAAO;AACtB,SAAK,UAAU,OAAO;AACtB,SAAK,YAAY,OAAO;AACxB,SAAK,aAAa,OAAO;AACzB,SAAK,aAAa,OAAO;AACzB,SAAK,UAAU,OAAO;AACtB,SAAK,WAAW,OAAO;AACvB,SAAK,aAAa,OAAO;AACzB,SAAK,aAAa,OAAO;AACzB,SAAK,WAAW,OAAO;AACvB,SAAK,aAAa,OAAO;AACzB,SAAK,YAAY,OAAO;AACxB,SAAK,oBAAoB,OAAO;AAAA,EACjC;AAAA,EA3CA,QAAiB,wBAAU,IAAY;AAAA,EAI9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAA8B;AAAA,EAC9B,YAA0D;AAAA,EAC1D,oBAAyD;AAAA,EAExD;AAAA,EA0BV,mBAAmB,OAAyB;AAC3C,WAAO;AAAA,EACR;AAAA,EAEA,iBAAiB,OAAyB;AACzC,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,sBAA+B;AAC9B,WAAO,KAAK,OAAO,cAAc,UAAa,KAAK,OAAO,UAAU,SAAS;AAAA,EAC9E;AACD;", "names": []}