---
import { verifySession } from '../../../lib/auth';
import { db, BlogPost } from 'astro:db';
import { eq, desc } from 'astro:db';
import AdminLayout from '../../../layouts/AdminLayout.astro';

// Verificar autenticación
const authToken = Astro.cookies.get('auth-token')?.value;
if (!authToken) {
  return Astro.redirect('/admin-finanzas-2024/login');
}

const session = await verifySession(authToken);
if (!session) {
  Astro.cookies.delete('auth-token');
  return Astro.redirect('/admin-finanzas-2024/login');
}

// Obtener parámetros de búsqueda y filtros
const url = new URL(Astro.request.url);
const searchQuery = url.searchParams.get('search') || '';
const statusFilter = url.searchParams.get('status') || 'all';
const page = parseInt(url.searchParams.get('page') || '1');
const limit = 10;
const offset = (page - 1) * limit;

// Manejar acciones POST
if (Astro.request.method === 'POST') {
  const formData = await Astro.request.formData();
  const action = formData.get('action');
  const postId = formData.get('postId');

  if (action === 'delete' && postId) {
    await db.delete(BlogPost).where(eq(BlogPost.id, parseInt(postId as string)));
    return Astro.redirect('/admin-finanzas-2024/blog');
  }

  if (action === 'toggle-status' && postId) {
    const post = await db.select().from(BlogPost).where(eq(BlogPost.id, parseInt(postId as string))).get();
    if (post) {
      await db.update(BlogPost)
        .set({
          published: !post.published,
          publishDate: !post.published ? new Date() : null,
          updatedAt: new Date()
        })
        .where(eq(BlogPost.id, parseInt(postId as string)));
    }
    return Astro.redirect('/admin-finanzas-2024/blog');
  }
}

// Construir query con filtros
let query = db.select().from(BlogPost);

// Aplicar filtros
if (statusFilter === 'published') {
  query = query.where(eq(BlogPost.published, true));
} else if (statusFilter === 'draft') {
  query = query.where(eq(BlogPost.published, false));
}

// Obtener posts con paginación
const allPosts = await query.orderBy(desc(BlogPost.updatedAt));
const filteredPosts = searchQuery
  ? allPosts.filter(post =>
      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.category.toLowerCase().includes(searchQuery.toLowerCase())
    )
  : allPosts;

const totalPosts = filteredPosts.length;
const totalPages = Math.ceil(totalPosts / limit);
const blogPosts = filteredPosts.slice(offset, offset + limit);

// Estadísticas
const publishedCount = allPosts.filter(p => p.published).length;
const draftCount = allPosts.filter(p => !p.published).length;
const featuredCount = allPosts.filter(p => p.featured).length;
---

<AdminLayout title="Gestión de Blog - Admin FinanzasReales">
  <!-- Navigation -->
  <nav class="admin-nav">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center space-x-4">
          <a href="/admin-finanzas-2024/dashboard" class="text-xl font-semibold text-gray-900">
            FinanzasReales Admin
          </a>
          <span class="text-gray-400">|</span>
          <span class="text-gray-600">Blog</span>
        </div>
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-700">
            {session.name}
          </span>
          <a
            href="/admin-finanzas-2024/logout"
            class="text-sm text-red-600 hover:text-red-800"
          >
            Cerrar Sesión
          </a>
        </div>
      </div>
    </div>
  </nav>

  <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Page header -->
    <div class="md:flex md:items-center md:justify-between mb-8">
      <div class="flex-1 min-w-0">
        <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
          Gestión de Blog
        </h2>
        <p class="mt-1 text-sm text-gray-500">
          Administra todos los artículos del blog
        </p>
      </div>
      <div class="mt-4 flex md:mt-0 md:ml-4">
        <a 
          href="/admin-finanzas-2024/blog/new"
          class="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          Nuevo Artículo
        </a>
      </div>
    </div>

    <!-- Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span class="text-blue-600 font-semibold">{blogPosts.length}</span>
              </div>
            </div>
            <div class="ml-5">
              <p class="text-sm font-medium text-gray-500">Total Artículos</p>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <span class="text-green-600 font-semibold">{blogPosts.filter(p => p.published).length}</span>
              </div>
            </div>
            <div class="ml-5">
              <p class="text-sm font-medium text-gray-500">Publicados</p>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <span class="text-yellow-600 font-semibold">{blogPosts.filter(p => !p.published).length}</span>
              </div>
            </div>
            <div class="ml-5">
              <p class="text-sm font-medium text-gray-500">Borradores</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Blog Posts Table -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
      <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
          Artículos del Blog
        </h3>
      </div>
      <ul class="divide-y divide-gray-200">
        {blogPosts.map(post => (
          <li>
            <div class="px-4 py-4 sm:px-6">
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <div class="flex items-center justify-between">
                    <p class="text-sm font-medium text-blue-600 truncate">
                      {post.title}
                    </p>
                    <div class="ml-2 flex-shrink-0 flex">
                      {post.published ? (
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          Publicado
                        </span>
                      ) : (
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                          Borrador
                        </span>
                      )}
                      {post.featured && (
                        <span class="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                          Destacado
                        </span>
                      )}
                    </div>
                  </div>
                  <div class="mt-2 sm:flex sm:justify-between">
                    <div class="sm:flex">
                      <p class="flex items-center text-sm text-gray-500">
                        <svg class="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-1.414.586H7a1 1 0 01-1-1V3a1 1 0 011-1z"></path>
                        </svg>
                        {post.category}
                      </p>
                      <p class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                        <svg class="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        {post.readTime}
                      </p>
                    </div>
                    <div class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                      <p>
                        Actualizado: {new Date(post.updatedAt).toLocaleDateString('es-ES')}
                      </p>
                    </div>
                  </div>
                  <div class="mt-2">
                    <p class="text-sm text-gray-600 line-clamp-2">
                      {post.excerpt}
                    </p>
                  </div>
                </div>
                <div class="ml-4 flex-shrink-0 flex space-x-2">
                  {post.published && (
                    <a 
                      href={`/blog/${post.slug}`}
                      target="_blank"
                      class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                      Ver
                    </a>
                  )}
                  <a 
                    href={`/admin-finanzas-2024/blog/edit/${post.id}`}
                    class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                  >
                    Editar
                  </a>
                  <form method="POST" class="inline" onsubmit="return confirm('¿Estás seguro de que quieres eliminar este artículo?')">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="postId" value={post.id}>
                    <button 
                      type="submit"
                      class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
                    >
                      Eliminar
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </li>
        ))}
      </ul>
      
      {blogPosts.length === 0 && (
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No hay artículos</h3>
          <p class="mt-1 text-sm text-gray-500">
            Comienza creando tu primer artículo del blog.
          </p>
          <div class="mt-6">
            <a 
              href="/admin-finanzas-2024/blog/new"
              class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
              Nuevo Artículo
            </a>
          </div>
        </div>
      )}
    </div>
  </div>

  <style>
    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  </style>
</body>
</html>
