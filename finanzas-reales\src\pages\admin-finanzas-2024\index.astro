---
// Página principal del admin - redirige al login o dashboard según autenticación
import { verifySession } from '../../lib/auth';

// Verificar si ya está autenticado
const authToken = Astro.cookies.get('auth-token')?.value;

if (authToken) {
  const session = await verifySession(authToken);
  if (session) {
    // Si está autenticado, redirigir al dashboard
    return Astro.redirect('/admin-finanzas-2024/dashboard');
  } else {
    // Token inválido, limpiar cookie y redirigir al login
    Astro.cookies.delete('auth-token');
  }
}

// Si no está autenticado, redirigir al login
return Astro.redirect('/admin-finanzas-2024/login');
---
