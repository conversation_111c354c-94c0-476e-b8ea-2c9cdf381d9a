import type { AddAliasToSelection } from "../query-builders/select.types.js";
import type { ColumnsSelection } from "../sql/sql.js";
import type { Subquery, WithSubquery } from "../subquery.js";
export type SubqueryWithSelection<TSelection extends ColumnsSelection, <PERSON><PERSON><PERSON>s extends string> = Subquery<TAlias, AddAliasToSelection<TSelection, TAlias, 'gel'>> & AddAliasToSelection<TSelection, TAlias, 'gel'>;
export type WithSubqueryWithSelection<TSelection extends ColumnsSelection, <PERSON><PERSON><PERSON><PERSON> extends string> = WithSubquery<TAlias, AddAliasToSelection<TSelection, TAlias, 'gel'>> & AddAliasToSelection<TSelection, TAlias, 'gel'>;
