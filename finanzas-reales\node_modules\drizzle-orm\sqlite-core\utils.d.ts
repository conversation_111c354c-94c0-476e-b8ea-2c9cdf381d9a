import type { Check } from "./checks.js";
import type { ForeignKey } from "./foreign-keys.js";
import type { Index } from "./indexes.js";
import type { PrimaryKey } from "./primary-keys.js";
import { SQLiteTable } from "./table.js";
import { type UniqueConstraint } from "./unique-constraint.js";
import type { SQLiteView } from "./view.js";
export declare function getTableConfig<TTable extends SQLiteTable>(table: TTable): {
    columns: import("./index.js").SQLiteColumn<any, {}, {}>[];
    indexes: Index[];
    foreignKeys: ForeignKey[];
    checks: Check[];
    primaryKeys: PrimaryKey[];
    uniqueConstraints: UniqueConstraint[];
    name: string;
};
export type OnConflict = 'rollback' | 'abort' | 'fail' | 'ignore' | 'replace';
export declare function getViewConfig<TName extends string = string, TExisting extends boolean = boolean>(view: SQLiteView<TName, TExisting>): {
    name: TName;
    originalName: TName;
    schema: string | undefined;
    selectedFields: import("../index.js").ColumnsSelection;
    isExisting: TExisting;
    query: TExisting extends true ? undefined : import("../index.js").SQL<unknown>;
    isAlias: boolean;
};
