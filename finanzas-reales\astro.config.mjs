// @ts-check
import { defineConfig } from 'astro/config';

import react from '@astrojs/react';
import tailwindcss from '@tailwindcss/vite';
import sitemap from '@astrojs/sitemap';
import db from '@astrojs/db';
import node from '@astrojs/node';

// https://astro.build/config
export default defineConfig({
  site: 'https://finanzasreales.com',
  output: 'server',
  adapter: node({
    mode: 'standalone'
  }),
  integrations: [
    react(),
    db({
      config: {
        url: 'file:./db/local.db'
      }
    }),
    sitemap({
      customPages: [
        'https://finanzasreales.com/perfiles/deudas-estudiantiles',
        'https://finanzasreales.com/perfiles/familia-monoparental',
        'https://finanzasreales.com/perfiles/ingresos-variables',
        'https://finanzasreales.com/historias/maria-deudas-estudiantiles',
        'https://finanzasreales.com/historias/carlos-ingresos-variables'
      ]
    })
  ],
  compressHTML: true,
  build: {
    inlineStylesheets: 'auto'
  },
  vite: {
    plugins: [tailwindcss()]
  }
});