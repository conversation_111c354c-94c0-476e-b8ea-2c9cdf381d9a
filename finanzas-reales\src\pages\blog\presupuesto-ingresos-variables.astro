---
import Layout from '../../layouts/Layout.astro';
---

<Layout title="Cómo Crear un Presupuesto que Funcione con Ingresos Variables - FinanzasReales">
	<article class="bg-white py-16">
		<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
			<!-- Article Header -->
			<header class="mb-12">
				<div class="text-center">
					<span class="bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full mb-4 inline-block">
						Presupuesto
					</span>
					<h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
						Cómo Crear un Presupuesto que Funcione con Ingresos Variables
					</h1>
					<p class="text-xl text-gray-600 mb-8">
						El método por percentiles que Carlos usa para manejar ingresos de €2K a €8K mensuales 
						y mantener estabilidad financiera.
					</p>
					<div class="flex items-center justify-center text-sm text-gray-500">
						<span>12 min de lectura</span>
						<span class="mx-2">•</span>
						<span>10 de mayo, 2024</span>
					</div>
				</div>
			</header>

			<!-- Article Content -->
			<div class="prose prose-lg max-w-none">
				<div class="bg-purple-50 border border-purple-200 rounded-lg p-6 mb-8">
					<h3 class="text-lg font-semibold text-purple-900 mb-2">Caso Real: Carlos</h3>
					<p class="text-purple-800">
						Carlos es consultor de marketing digital con ingresos que varían entre €2,000 y €8,000 mensuales. 
						Desarrolló un sistema que le permite ahorrar consistentemente y mantener estabilidad financiera 
						sin importar la variabilidad de sus ingresos.
					</p>
				</div>

				<h2>El problema con los presupuestos tradicionales</h2>
				<p>
					Los presupuestos tradicionales asumen ingresos fijos y predecibles. Cuando tus ingresos varían significativamente, 
					estos métodos fallan porque:
				</p>
				<ul>
					<li>No puedes comprometerte a gastos fijos altos</li>
					<li>Los meses buenos te hacen gastar más de lo que deberías</li>
					<li>Los meses malos generan estrés y decisiones financieras pobres</li>
					<li>Es difícil ahorrar consistentemente</li>
				</ul>

				<h2>El método por percentiles de Carlos</h2>
				<p>
					Carlos desarrolló un sistema basado en el análisis estadístico de sus ingresos históricos:
				</p>

				<h3>Paso 1: Analiza tu historial de ingresos</h3>
				<p>
					Recopila al menos 12 meses de datos de ingresos y calcula:
				</p>
				<ul>
					<li><strong>Percentil 25 (P25):</strong> El 25% de tus meses tienen ingresos iguales o menores a esta cantidad</li>
					<li><strong>Percentil 50 (P50):</strong> La mediana de tus ingresos</li>
					<li><strong>Percentil 75 (P75):</strong> El 75% de tus meses tienen ingresos iguales o menores a esta cantidad</li>
				</ul>

				<div class="bg-blue-50 border border-blue-200 rounded-lg p-6 my-8">
					<h3 class="text-lg font-semibold text-blue-900 mb-3">Ejemplo: Datos de Carlos</h3>
					<div class="text-blue-800">
						<p><strong>Ingresos de 12 meses:</strong> €2,000, €3,500, €4,200, €2,800, €6,500, €3,200, €7,800, €4,500, €2,500, €5,200, €8,000, €3,800</p>
						<p><strong>Ordenados:</strong> €2,000, €2,500, €2,800, €3,200, €3,500, €3,800, €4,200, €4,500, €5,200, €6,500, €7,800, €8,000</p>
						<ul class="mt-3">
							<li><strong>P25:</strong> €3,050 (presupuesto conservador)</li>
							<li><strong>P50:</strong> €3,900 (presupuesto moderado)</li>
							<li><strong>P75:</strong> €5,850 (presupuesto optimista)</li>
						</ul>
					</div>
				</div>

				<h3>Paso 2: Crea tres niveles de presupuesto</h3>
				<h4>Presupuesto Base (P25): €3,050</h4>
				<p>
					Este es tu presupuesto de supervivencia. Incluye solo gastos absolutamente esenciales:
				</p>
				<ul>
					<li>Vivienda: €1,200</li>
					<li>Alimentación básica: €400</li>
					<li>Transporte: €200</li>
					<li>Seguros: €150</li>
					<li>Servicios básicos: €100</li>
					<li>Ahorros mínimos: €600 (20%)</li>
					<li>Gastos varios: €400</li>
				</ul>

				<h4>Presupuesto Moderado (P50): €3,900</h4>
				<p>
					Agrega algunos gastos de calidad de vida:
				</p>
				<ul>
					<li>Todo lo del presupuesto base</li>
					<li>Entretenimiento: €200</li>
					<li>Comidas fuera: €150</li>
					<li>Ahorros adicionales: €200</li>
					<li>Gastos personales: €350</li>
				</ul>

				<h4>Presupuesto Optimista (P75): €5,850</h4>
				<p>
					Para meses de ingresos altos:
				</p>
				<ul>
					<li>Todo lo del presupuesto moderado</li>
					<li>Ahorros agresivos: €1,000 extra</li>
					<li>Inversiones en el negocio: €500</li>
					<li>Gastos de lujo: €450</li>
				</ul>

				<h3>Paso 3: Automatiza según el ingreso del mes</h3>
				<p>
					Carlos configuró su sistema bancario para que automáticamente:
				</p>
				<ul>
					<li>Si el ingreso ≤ P25: Usa presupuesto base</li>
					<li>Si P25 < ingreso ≤ P50: Usa presupuesto moderado</li>
					<li>Si ingreso > P50: Usa presupuesto optimista</li>
				</ul>

				<h2>Estrategias adicionales para ingresos variables</h2>
				<h3>1. Fondo de estabilización</h3>
				<p>
					Diferente al fondo de emergencia, este fondo suaviza las variaciones mensuales. 
					Carlos mantiene €9,000 (3 meses de gastos) específicamente para esto.
				</p>

				<h3>2. Ahorro por porcentajes, no cantidades fijas</h3>
				<p>
					En lugar de "ahorrar €500/mes", Carlos ahorra:
				</p>
				<ul>
					<li>20% en meses de ingresos bajos (P25)</li>
					<li>25% en meses promedio (P50)</li>
					<li>35% en meses altos (P75+)</li>
				</ul>

				<h3>3. Separación estricta de finanzas</h3>
				<p>
					Carlos mantiene cuentas completamente separadas:
				</p>
				<ul>
					<li><strong>Cuenta de negocio:</strong> Todos los ingresos llegan aquí</li>
					<li><strong>Cuenta personal:</strong> Se paga un "salario" fijo mensual</li>
					<li><strong>Cuenta de impuestos:</strong> 25% de todos los ingresos</li>
					<li><strong>Cuenta de estabilización:</strong> Excesos de meses buenos</li>
				</ul>

				<div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 my-8">
					<h3 class="text-lg font-semibold text-yellow-900 mb-3">💡 Consejo Clave</h3>
					<p class="text-yellow-800">
						La clave del éxito de Carlos es que <strong>nunca</strong> gasta basándose en sus ingresos más altos. 
						Siempre asume que el próximo mes podría ser un mes P25 y planifica en consecuencia.
					</p>
				</div>

				<h2>Herramientas que Carlos usa</h2>
				<h3>1. Holded para gestión financiera</h3>
				<p>
					Automatiza la separación de gastos personales vs. de negocio y genera reportes fiscales automáticamente.
				</p>

				<h3>2. N26 Business para banca</h3>
				<p>
					Múltiples cuentas con insights en tiempo real y transferencias automáticas programables.
				</p>

				<h3>3. Spreadsheet personalizado</h3>
				<p>
					Para tracking de percentiles y ajuste mensual del presupuesto.
				</p>

				<h2>Resultados de Carlos después de 18 meses</h2>
				<ul>
					<li><strong>Fondo de estabilización:</strong> €18,000 (9 meses de gastos)</li>
					<li><strong>Inversiones mensuales:</strong> €400 consistentes</li>
					<li><strong>Estrés financiero:</strong> Prácticamente eliminado</li>
					<li><strong>Ingresos promedio:</strong> Aumentaron de €4,200 a €5,200/mes</li>
					<li><strong>Variabilidad:</strong> Reducida del 150% al 60%</li>
				</ul>

				<h2>Cómo implementar este sistema</h2>
				<h3>Semana 1: Recopila datos</h3>
				<ul>
					<li>Descarga al menos 12 meses de estados de cuenta</li>
					<li>Categoriza todos los ingresos por mes</li>
					<li>Calcula tus percentiles</li>
				</ul>

				<h3>Semana 2: Diseña tus presupuestos</h3>
				<ul>
					<li>Crea presupuesto base (P25)</li>
					<li>Expande a presupuesto moderado (P50)</li>
					<li>Define presupuesto optimista (P75)</li>
				</ul>

				<h3>Semana 3: Configura automatización</h3>
				<ul>
					<li>Abre cuentas separadas si es necesario</li>
					<li>Configura transferencias automáticas</li>
					<li>Instala herramientas de tracking</li>
				</ul>

				<h3>Semana 4: Prueba y ajusta</h3>
				<ul>
					<li>Implementa el sistema por un mes</li>
					<li>Identifica problemas y ajusta</li>
					<li>Refina las categorías de gastos</li>
				</ul>

				<div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mt-8">
					<h3 class="text-lg font-semibold text-gray-900 mb-3">¿Quieres la plantilla de Carlos?</h3>
					<p class="text-gray-700 mb-4">
						Descarga la misma plantilla que Carlos desarrolló para gestionar sus ingresos variables, 
						incluyendo calculadoras automáticas de percentiles y presupuestos adaptativos.
					</p>
					<a href="/descargas/presupuesto-ingresos-variables" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
						Descargar Plantilla Gratis
					</a>
				</div>
			</div>
		</div>
	</article>
</Layout>
