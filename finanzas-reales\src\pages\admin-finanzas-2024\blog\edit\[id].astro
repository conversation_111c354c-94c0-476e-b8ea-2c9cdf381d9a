---
import { verifySession } from '../../../../lib/auth';
import { db, BlogPost } from 'astro:db';
import { eq } from 'astro:db';
import AdminLayout from '../../../../layouts/AdminLayout.astro';

// Verificar autenticación
const authToken = Astro.cookies.get('auth-token')?.value;
if (!authToken) {
  return Astro.redirect('/admin-finanzas-2024/login');
}

const session = await verifySession(authToken);
if (!session) {
  Astro.cookies.delete('auth-token');
  return Astro.redirect('/admin-finanzas-2024/login');
}

// Obtener ID del artículo
const { id } = Astro.params;
if (!id) {
  return Astro.redirect('/admin-finanzas-2024/blog');
}

// Obtener el artículo
const post = await db.select().from(BlogPost).where(eq(BlogPost.id, parseInt(id))).get();
if (!post) {
  return Astro.redirect('/admin-finanzas-2024/blog');
}

let error = '';
let success = false;

// Manejar actualización del artículo
if (Astro.request.method === 'POST') {
  try {
    const formData = await Astro.request.formData();
    const title = formData.get('title') as string;
    const excerpt = formData.get('excerpt') as string;
    const content = formData.get('content') as string;
    const category = formData.get('category') as string;
    const tags = formData.get('tags') as string;
    const readTime = formData.get('readTime') as string;
    const featured = formData.has('featured');
    const published = formData.has('published');

    if (!title || !excerpt || !content || !category) {
      error = 'Por favor completa todos los campos obligatorios';
    } else {
      // Generar slug del título si ha cambiado
      let slug = post.slug;
      if (title !== post.title) {
        slug = title
          .toLowerCase()
          .normalize('NFD')
          .replace(/[\u0300-\u036f]/g, '')
          .replace(/[^a-z0-9\s-]/g, '')
          .trim()
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-');
        
        // Verificar que el nuevo slug no exista
        const existingPosts = await db.select().from(BlogPost)
          .where(eq(BlogPost.slug, slug));
        
        if (existingPosts.length > 0 && existingPosts[0].id !== post.id) {
          slug = `${slug}-${Date.now()}`;
        }
      }
      
      const tagsArray = tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
      
      await db.update(BlogPost)
        .set({
          slug,
          title,
          excerpt,
          content,
          category,
          tags: JSON.stringify(tagsArray),
          readTime: readTime || '5 min',
          featured,
          published,
          publishDate: published && !post.published ? new Date() : (published ? post.publishDate : null),
          updatedAt: new Date(),
        })
        .where(eq(BlogPost.id, post.id));

      success = true;
      // Actualizar el objeto post para mostrar los nuevos datos
      Object.assign(post, {
        slug,
        title,
        excerpt,
        content,
        category,
        tags: JSON.stringify(tagsArray),
        readTime: readTime || '5 min',
        featured,
        published,
        updatedAt: new Date(),
      });
    }
  } catch (e) {
    console.error('Error updating blog post:', e);
    error = 'Error interno del servidor';
  }
}

const categories = [
  'Estrategias de Deuda',
  'Presupuesto',
  'Ahorro',
  'Emprendimiento',
  'Automatización',
  'Planificación',
  'Inversión',
  'Seguros',
  'Impuestos'
];

// Parsear tags existentes
const existingTags = post.tags ? JSON.parse(post.tags) : [];
---

<AdminLayout title={`Editar: ${post.title} - Admin FinanzasReales`}>
  <!-- Navigation -->
  <nav class="admin-nav">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center space-x-4">
          <a href="/admin-finanzas-2024/dashboard" class="text-xl font-semibold text-gray-900">
            FinanzasReales Admin
          </a>
          <span class="text-gray-400">|</span>
          <a href="/admin-finanzas-2024/blog" class="text-gray-600 hover:text-gray-900">Blog</a>
          <span class="text-gray-400">|</span>
          <span class="text-gray-600">Editar Artículo</span>
        </div>
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-700">
            {session.name}
          </span>
          <a 
            href="/admin-finanzas-2024/logout" 
            class="text-sm text-red-600 hover:text-red-800"
          >
            Cerrar Sesión
          </a>
        </div>
      </div>
    </div>
  </nav>

  <div class="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Page header -->
    <div class="mb-8">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        Editar Artículo
      </h2>
      <p class="mt-1 text-sm text-gray-500">
        Modifica la información del artículo "{post.title}"
      </p>
    </div>

    {error && (
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
        {error}
      </div>
    )}

    {success && (
      <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
        Artículo actualizado exitosamente.
      </div>
    )}

    <form method="POST" class="space-y-6">
      <div class="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
        <div class="md:grid md:grid-cols-3 md:gap-6">
          <div class="md:col-span-1">
            <h3 class="text-lg font-medium leading-6 text-gray-900">Información Básica</h3>
            <p class="mt-1 text-sm text-gray-500">
              Información principal del artículo
            </p>
          </div>
          <div class="mt-5 md:mt-0 md:col-span-2">
            <div class="grid grid-cols-6 gap-6">
              <div class="col-span-6">
                <label for="title" class="block text-sm font-medium text-gray-700">
                  Título *
                </label>
                <input 
                  type="text" 
                  name="title" 
                  id="title" 
                  required
                  value={post.title}
                  class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                  placeholder="Título del artículo"
                >
              </div>

              <div class="col-span-6">
                <label for="excerpt" class="block text-sm font-medium text-gray-700">
                  Extracto *
                </label>
                <textarea 
                  name="excerpt" 
                  id="excerpt" 
                  rows="3" 
                  required
                  class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                  placeholder="Breve descripción del artículo"
                >{post.excerpt}</textarea>
              </div>

              <div class="col-span-3">
                <label for="category" class="block text-sm font-medium text-gray-700">
                  Categoría *
                </label>
                <select 
                  name="category" 
                  id="category" 
                  required
                  class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="">Selecciona una categoría</option>
                  {categories.map(cat => (
                    <option value={cat} selected={cat === post.category}>{cat}</option>
                  ))}
                </select>
              </div>

              <div class="col-span-3">
                <label for="readTime" class="block text-sm font-medium text-gray-700">
                  Tiempo de Lectura
                </label>
                <input 
                  type="text" 
                  name="readTime" 
                  id="readTime"
                  value={post.readTime}
                  placeholder="5 min"
                  class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                >
              </div>

              <div class="col-span-6">
                <label for="tags" class="block text-sm font-medium text-gray-700">
                  Etiquetas
                </label>
                <input 
                  type="text" 
                  name="tags" 
                  id="tags"
                  value={existingTags.join(', ')}
                  placeholder="deudas, presupuesto, ahorro (separadas por comas)"
                  class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                >
                <p class="mt-1 text-sm text-gray-500">
                  Separa las etiquetas con comas
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
        <div class="md:grid md:grid-cols-3 md:gap-6">
          <div class="md:col-span-1">
            <h3 class="text-lg font-medium leading-6 text-gray-900">Contenido</h3>
            <p class="mt-1 text-sm text-gray-500">
              Contenido principal del artículo
            </p>
          </div>
          <div class="mt-5 md:mt-0 md:col-span-2">
            <div>
              <label for="content" class="block text-sm font-medium text-gray-700">
                Contenido del Artículo *
              </label>
              <textarea
                name="content"
                id="content"
                rows="20"
                required
                class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                placeholder="Escribe el contenido del artículo aquí..."
              >{post.content}</textarea>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
        <div class="md:grid md:grid-cols-3 md:gap-6">
          <div class="md:col-span-1">
            <h3 class="text-lg font-medium leading-6 text-gray-900">Configuración</h3>
            <p class="mt-1 text-sm text-gray-500">
              Opciones de publicación
            </p>
          </div>
          <div class="mt-5 md:mt-0 md:col-span-2">
            <div class="space-y-4">
              <div class="flex items-start">
                <div class="flex items-center h-5">
                  <input
                    id="featured"
                    name="featured"
                    type="checkbox"
                    checked={post.featured}
                    class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
                  >
                </div>
                <div class="ml-3 text-sm">
                  <label for="featured" class="font-medium text-gray-700">
                    Artículo Destacado
                  </label>
                  <p class="text-gray-500">
                    Mostrar este artículo en la sección destacada
                  </p>
                </div>
              </div>

              <div class="flex items-start">
                <div class="flex items-center h-5">
                  <input
                    id="published"
                    name="published"
                    type="checkbox"
                    checked={post.published}
                    class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
                  >
                </div>
                <div class="ml-3 text-sm">
                  <label for="published" class="font-medium text-gray-700">
                    Publicado
                  </label>
                  <p class="text-gray-500">
                    {post.published ? 'El artículo está publicado' : 'Marcar para publicar el artículo'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex justify-between">
        <div class="flex space-x-3">
          <a
            href="/admin-finanzas-2024/blog"
            class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            ← Volver al Blog
          </a>
          {post.published && (
            <a
              href={`/blog/${post.slug}`}
              target="_blank"
              class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Ver Artículo
            </a>
          )}
        </div>
        <button
          type="submit"
          class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          Actualizar Artículo
        </button>
      </div>
    </form>
  </div>

  <script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin" is:inline></script>
  <script is:inline>
    // Inicializar TinyMCE para el editor de contenido
    document.addEventListener('DOMContentLoaded', function() {
      tinymce.init({
        selector: '#content',
        height: 400,
        menubar: false,
        plugins: [
          'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
          'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
          'insertdatetime', 'media', 'table', 'help', 'wordcount'
        ],
        toolbar: 'undo redo | blocks | ' +
          'bold italic backcolor | alignleft aligncenter ' +
          'alignright alignjustify | bullist numlist outdent indent | ' +
          'removeformat | help',
        content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }'
      });
    });
  </script>
</AdminLayout>
