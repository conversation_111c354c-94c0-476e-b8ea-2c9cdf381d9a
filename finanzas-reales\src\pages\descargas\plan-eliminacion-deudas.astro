---
import Layout from '../../layouts/Layout.astro';
---

<Layout title="Descarga: Plan de Eliminación de Deudas - FinanzasReales">
	<section class="bg-white py-16">
		<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center mb-8">
				<h1 class="text-4xl font-bold text-gray-900 mb-4">
					Plan de Eliminación de Deudas
				</h1>
				<p class="text-xl text-gray-600 mb-8">
					La plantilla exacta que usó María para eliminar €45,000 en deudas estudiantiles
				</p>
			</div>

			<div class="bg-blue-50 border border-blue-200 rounded-xl p-8 mb-8">
				<h2 class="text-2xl font-bold text-blue-900 mb-4">¿Qué incluye esta plantilla?</h2>
				<ul class="space-y-3 text-blue-800">
					<li class="flex items-start">
						<svg class="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
						</svg>
						Calculadora automática de métodos Avalancha vs. Bola de Nieve
					</li>
					<li class="flex items-start">
						<svg class="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
						</svg>
						Cronograma de pagos personalizable
					</li>
					<li class="flex items-start">
						<svg class="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
						</svg>
						Tracker de progreso visual
					</li>
					<li class="flex items-start">
						<svg class="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
						</svg>
						Calculadora de ahorros en intereses
					</li>
					<li class="flex items-start">
						<svg class="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
						</svg>
						Instrucciones paso a paso basadas en el caso de María
					</li>
				</ul>
			</div>

			<div class="bg-green-50 border border-green-200 rounded-xl p-8 mb-8">
				<h3 class="text-xl font-bold text-green-900 mb-3">Historia de Éxito: María</h3>
				<p class="text-green-800 mb-4">
					María usó exactamente esta plantilla para eliminar €45,000 en deudas estudiantiles en 3 años, 
					ahorrando €12,000 en intereses comparado con el plan de pagos original.
				</p>
				<a href="/historias/maria-deudas-estudiantiles" class="text-green-600 hover:text-green-700 font-medium">
					Leer la historia completa de María →
				</a>
			</div>

			<form class="bg-gray-50 rounded-xl p-8" id="download-form">
				<h3 class="text-xl font-bold text-gray-900 mb-6">Descarga Gratuita</h3>
				
				<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
					<div>
						<label for="name" class="block text-sm font-medium text-gray-700 mb-2">
							Nombre *
						</label>
						<input 
							type="text" 
							id="name" 
							name="name" 
							required
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
							placeholder="Tu nombre"
						>
					</div>
					
					<div>
						<label for="email" class="block text-sm font-medium text-gray-700 mb-2">
							Email *
						</label>
						<input 
							type="email" 
							id="email" 
							name="email" 
							required
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
							placeholder="<EMAIL>"
						>
					</div>
				</div>

				<div class="mb-6">
					<label for="debt-amount" class="block text-sm font-medium text-gray-700 mb-2">
						¿Cuánta deuda tienes aproximadamente? (Opcional)
					</label>
					<select 
						id="debt-amount" 
						name="debt-amount"
						class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
					>
						<option value="">Selecciona un rango</option>
						<option value="0-5000">€0 - €5,000</option>
						<option value="5000-15000">€5,000 - €15,000</option>
						<option value="15000-30000">€15,000 - €30,000</option>
						<option value="30000-50000">€30,000 - €50,000</option>
						<option value="50000+">Más de €50,000</option>
					</select>
				</div>
				
				<div class="flex items-start mb-6">
					<input 
						type="checkbox" 
						id="newsletter" 
						name="newsletter"
						class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
					>
					<label for="newsletter" class="ml-2 text-sm text-gray-700">
						Quiero recibir consejos financieros y nuevos recursos por email (opcional)
					</label>
				</div>
				
				<div class="flex items-start mb-6">
					<input 
						type="checkbox" 
						id="privacy" 
						name="privacy" 
						required
						class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
					>
					<label for="privacy" class="ml-2 text-sm text-gray-700">
						Acepto la <a href="/privacidad" class="text-blue-600 hover:text-blue-700">política de privacidad</a> *
					</label>
				</div>
				
				<button 
					type="submit"
					class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
				>
					Descargar Plantilla Excel Gratis
				</button>
			</form>

			<div class="mt-8 text-center">
				<p class="text-sm text-gray-600">
					¿Problemas con la descarga? <a href="/contacto" class="text-blue-600 hover:text-blue-700">Contáctanos</a>
				</p>
			</div>
		</div>
	</section>
</Layout>

<script>
	document.getElementById('download-form')?.addEventListener('submit', function(e) {
		e.preventDefault();
		
		// Simulate download process
		const button = this.querySelector('button[type="submit"]');
		const originalText = button.textContent;
		
		button.textContent = 'Preparando descarga...';
		button.disabled = true;
		
		setTimeout(() => {
			// In a real implementation, this would trigger the actual download
			// For now, we'll show a success message
			button.textContent = '✓ ¡Descarga iniciada!';
			button.classList.remove('bg-blue-600', 'hover:bg-blue-700');
			button.classList.add('bg-green-600');
			
			// Create a mock download link
			const link = document.createElement('a');
			link.href = '/assets/plantillas/plan-eliminacion-deudas-maria.xlsx';
			link.download = 'Plan-Eliminacion-Deudas-FinanzasReales.xlsx';
			link.click();
			
			setTimeout(() => {
				button.textContent = originalText;
				button.disabled = false;
				button.classList.remove('bg-green-600');
				button.classList.add('bg-blue-600', 'hover:bg-blue-700');
			}, 3000);
		}, 2000);
	});
</script>
