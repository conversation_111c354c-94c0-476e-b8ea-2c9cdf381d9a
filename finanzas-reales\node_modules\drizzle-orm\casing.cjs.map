{"version": 3, "sources": ["../src/casing.ts"], "sourcesContent": ["import type { Column } from '~/column.ts';\nimport { entityKind } from './entity.ts';\nimport { Table } from './table.ts';\nimport type { Casing } from './utils.ts';\n\nexport function toSnakeCase(input: string) {\n\tconst words = input\n\t\t.replace(/['\\u2019]/g, '')\n\t\t.match(/[\\da-z]+|[A-Z]+(?![a-z])|[A-Z][\\da-z]+/g) ?? [];\n\n\treturn words.map((word) => word.toLowerCase()).join('_');\n}\n\nexport function toCamelCase(input: string) {\n\tconst words = input\n\t\t.replace(/['\\u2019]/g, '')\n\t\t.match(/[\\da-z]+|[A-Z]+(?![a-z])|[A-Z][\\da-z]+/g) ?? [];\n\n\treturn words.reduce((acc, word, i) => {\n\t\tconst formattedWord = i === 0 ? word.toLowerCase() : `${word[0]!.toUpperCase()}${word.slice(1)}`;\n\t\treturn acc + formattedWord;\n\t}, '');\n}\n\nfunction noopCase(input: string) {\n\treturn input;\n}\n\nexport class CasingCache {\n\tstatic readonly [entityKind]: string = 'CasingCache';\n\n\t/** @internal */\n\tcache: Record<string, string> = {};\n\tprivate cachedTables: Record<string, true> = {};\n\tprivate convert: (input: string) => string;\n\n\tconstructor(casing?: Casing) {\n\t\tthis.convert = casing === 'snake_case'\n\t\t\t? toSnakeCase\n\t\t\t: casing === 'camelCase'\n\t\t\t? toCamelCase\n\t\t\t: noopCase;\n\t}\n\n\tgetColumnCasing(column: Column): string {\n\t\tif (!column.keyAsName) return column.name;\n\n\t\tconst schema = column.table[Table.Symbol.Schema] ?? 'public';\n\t\tconst tableName = column.table[Table.Symbol.OriginalName];\n\t\tconst key = `${schema}.${tableName}.${column.name}`;\n\n\t\tif (!this.cache[key]) {\n\t\t\tthis.cacheTable(column.table);\n\t\t}\n\t\treturn this.cache[key]!;\n\t}\n\n\tprivate cacheTable(table: Table) {\n\t\tconst schema = table[Table.Symbol.Schema] ?? 'public';\n\t\tconst tableName = table[Table.Symbol.OriginalName];\n\t\tconst tableKey = `${schema}.${tableName}`;\n\n\t\tif (!this.cachedTables[tableKey]) {\n\t\t\tfor (const column of Object.values(table[Table.Symbol.Columns])) {\n\t\t\t\tconst columnKey = `${tableKey}.${column.name}`;\n\t\t\t\tthis.cache[columnKey] = this.convert(column.name);\n\t\t\t}\n\t\t\tthis.cachedTables[tableKey] = true;\n\t\t}\n\t}\n\n\tclearCache() {\n\t\tthis.cache = {};\n\t\tthis.cachedTables = {};\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA2B;AAC3B,mBAAsB;AAGf,SAAS,YAAY,OAAe;AAC1C,QAAM,QAAQ,MACZ,QAAQ,cAAc,EAAE,EACxB,MAAM,yCAAyC,KAAK,CAAC;AAEvD,SAAO,MAAM,IAAI,CAAC,SAAS,KAAK,YAAY,CAAC,EAAE,KAAK,GAAG;AACxD;AAEO,SAAS,YAAY,OAAe;AAC1C,QAAM,QAAQ,MACZ,QAAQ,cAAc,EAAE,EACxB,MAAM,yCAAyC,KAAK,CAAC;AAEvD,SAAO,MAAM,OAAO,CAAC,KAAK,MAAM,MAAM;AACrC,UAAM,gBAAgB,MAAM,IAAI,KAAK,YAAY,IAAI,GAAG,KAAK,CAAC,EAAG,YAAY,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC;AAC9F,WAAO,MAAM;AAAA,EACd,GAAG,EAAE;AACN;AAEA,SAAS,SAAS,OAAe;AAChC,SAAO;AACR;AAEO,MAAM,YAAY;AAAA,EACxB,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAGvC,QAAgC,CAAC;AAAA,EACzB,eAAqC,CAAC;AAAA,EACtC;AAAA,EAER,YAAY,QAAiB;AAC5B,SAAK,UAAU,WAAW,eACvB,cACA,WAAW,cACX,cACA;AAAA,EACJ;AAAA,EAEA,gBAAgB,QAAwB;AACvC,QAAI,CAAC,OAAO;AAAW,aAAO,OAAO;AAErC,UAAM,SAAS,OAAO,MAAM,mBAAM,OAAO,MAAM,KAAK;AACpD,UAAM,YAAY,OAAO,MAAM,mBAAM,OAAO,YAAY;AACxD,UAAM,MAAM,GAAG,MAAM,IAAI,SAAS,IAAI,OAAO,IAAI;AAEjD,QAAI,CAAC,KAAK,MAAM,GAAG,GAAG;AACrB,WAAK,WAAW,OAAO,KAAK;AAAA,IAC7B;AACA,WAAO,KAAK,MAAM,GAAG;AAAA,EACtB;AAAA,EAEQ,WAAW,OAAc;AAChC,UAAM,SAAS,MAAM,mBAAM,OAAO,MAAM,KAAK;AAC7C,UAAM,YAAY,MAAM,mBAAM,OAAO,YAAY;AACjD,UAAM,WAAW,GAAG,MAAM,IAAI,SAAS;AAEvC,QAAI,CAAC,KAAK,aAAa,QAAQ,GAAG;AACjC,iBAAW,UAAU,OAAO,OAAO,MAAM,mBAAM,OAAO,OAAO,CAAC,GAAG;AAChE,cAAM,YAAY,GAAG,QAAQ,IAAI,OAAO,IAAI;AAC5C,aAAK,MAAM,SAAS,IAAI,KAAK,QAAQ,OAAO,IAAI;AAAA,MACjD;AACA,WAAK,aAAa,QAAQ,IAAI;AAAA,IAC/B;AAAA,EACD;AAAA,EAEA,aAAa;AACZ,SAAK,QAAQ,CAAC;AACd,SAAK,eAAe,CAAC;AAAA,EACtB;AACD;", "names": []}