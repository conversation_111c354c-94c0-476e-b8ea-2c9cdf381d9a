---
export interface Props {
	title: string;
}

const { title } = Astro.props;
---

<!DOCTYPE html>
<html lang="es">
	<head>
		<meta charset="UTF-8" />
		<meta name="description" content="Historias financieras reales de personas como tú. Encuentra soluciones prácticas basadas en situaciones auténticas." />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<title>{title}</title>
		
		<!-- Open Graph / Facebook -->
		<meta property="og:type" content="website" />
		<meta property="og:title" content={title} />
		<meta property="og:description" content="Historias financieras reales de personas como tú. Encuentra soluciones prácticas basadas en situaciones auténticas." />
		
		<!-- Twitter -->
		<meta property="twitter:card" content="summary_large_image" />
		<meta property="twitter:title" content={title} />
		<meta property="twitter:description" content="Historias financieras reales de personas como tú. Encuentra soluciones prácticas basadas en situaciones auténticas." />
		
		<!-- Fonts -->
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
		<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
	</head>
	<body class="font-inter bg-gray-50">
		<header class="bg-white shadow-sm border-b border-gray-200">
			<nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div class="flex justify-between items-center h-16">
					<div class="flex items-center">
						<a href="/" class="text-2xl font-bold text-blue-600">
							FinanzasReales
						</a>
					</div>
					<div class="hidden md:block">
						<div class="ml-10 flex items-baseline space-x-4">
							<a href="/historias" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors">
								Historias
							</a>
							<a href="/calculadoras" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors">
								Calculadoras
							</a>
							<a href="/perfiles" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors">
								Perfiles
							</a>
							<a href="/recursos" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors">
								Recursos
							</a>
							<a href="/blog" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors">
								Blog
							</a>
						</div>
					</div>
					<div class="md:hidden">
						<button type="button" class="text-gray-700 hover:text-blue-600 focus:outline-none focus:text-blue-600">
							<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
							</svg>
						</button>
					</div>
				</div>
			</nav>
		</header>

		<main>
			<slot />
		</main>

		<footer class="bg-gray-900 text-white">
			<div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
				<div class="grid grid-cols-1 md:grid-cols-4 gap-8">
					<div class="col-span-1 md:col-span-2">
						<h3 class="text-2xl font-bold mb-4">FinanzasReales</h3>
						<p class="text-gray-300 mb-4">
							Historias financieras auténticas de personas reales. Encuentra soluciones prácticas basadas en situaciones similares a la tuya.
						</p>
						<div class="flex space-x-4">
							<a href="#" class="text-gray-300 hover:text-white transition-colors">
								<span class="sr-only">Facebook</span>
								<svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
									<path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"/>
								</svg>
							</a>
							<a href="#" class="text-gray-300 hover:text-white transition-colors">
								<span class="sr-only">Twitter</span>
								<svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
									<path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
								</svg>
							</a>
						</div>
					</div>
					<div>
						<h4 class="text-lg font-semibold mb-4">Navegación</h4>
						<ul class="space-y-2">
							<li><a href="/historias" class="text-gray-300 hover:text-white transition-colors">Historias</a></li>
							<li><a href="/calculadoras" class="text-gray-300 hover:text-white transition-colors">Calculadoras</a></li>
							<li><a href="/perfiles" class="text-gray-300 hover:text-white transition-colors">Perfiles</a></li>
							<li><a href="/recursos" class="text-gray-300 hover:text-white transition-colors">Recursos</a></li>
						</ul>
					</div>
					<div>
						<h4 class="text-lg font-semibold mb-4">Legal</h4>
						<ul class="space-y-2">
							<li><a href="/privacidad" class="text-gray-300 hover:text-white transition-colors">Política de Privacidad</a></li>
							<li><a href="/terminos" class="text-gray-300 hover:text-white transition-colors">Términos de Uso</a></li>
							<li><a href="/afiliados" class="text-gray-300 hover:text-white transition-colors">Divulgación de Afiliados</a></li>
							<li><a href="/contacto" class="text-gray-300 hover:text-white transition-colors">Contacto</a></li>
						</ul>
					</div>
				</div>
				<div class="mt-8 pt-8 border-t border-gray-700 text-center">
					<p class="text-gray-300">
						© 2024 FinanzasReales. Todos los derechos reservados.
					</p>
				</div>
			</div>
		</footer>

		<style>
			.font-inter {
				font-family: 'Inter', sans-serif;
			}
		</style>
		
		<!-- Import global styles -->
		<style is:global>
			@import '../styles/global.css';
		</style>
	</body>
</html>
