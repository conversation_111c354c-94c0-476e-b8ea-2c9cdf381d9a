---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Recursos Financieros Gratuitos - FinanzasReales">
	<!-- Hero Section -->
	<section class="bg-gradient-to-br from-teal-50 to-cyan-100 py-16">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center">
				<h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
					Recursos Financieros Gratuitos
				</h1>
				<p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
					Plantillas, guías y herramientas probadas en historias reales. 
					Todo lo que necesitas para implementar las estrategias que han funcionado.
				</p>
			</div>
		</div>
	</section>

	<!-- Resource Categories -->
	<section class="bg-white py-16">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			
			<!-- Templates Section -->
			<div class="mb-16">
				<h2 class="text-3xl font-bold text-gray-900 text-center mb-12">
					Plantillas Descargables
				</h2>
				
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
					<!-- Template 1 -->
					<div class="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300">
						<div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
							<svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
							</svg>
						</div>
						<h3 class="text-xl font-bold text-gray-900 mb-3">
							Plan de Eliminación de Deudas
						</h3>
						<p class="text-gray-600 mb-4">
							La plantilla exacta que usó María para eliminar €45,000 en deudas estudiantiles. 
							Incluye calculadora de métodos avalancha vs. bola de nieve.
						</p>
						<div class="flex items-center justify-between">
							<span class="text-sm text-green-600 font-medium">Basado en historia real</span>
							<a href="/descargas/plan-eliminacion-deudas" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
								Descargar Excel
							</a>
						</div>
					</div>

					<!-- Template 2 -->
					<div class="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300">
						<div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
							<svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
							</svg>
						</div>
						<h3 class="text-xl font-bold text-gray-900 mb-3">
							Presupuesto para Ingresos Variables
						</h3>
						<p class="text-gray-600 mb-4">
							El sistema que Carlos desarrolló para manejar ingresos de €2K-€8K mensuales. 
							Incluye automatización y fórmulas de ahorro adaptativo.
						</p>
						<div class="flex items-center justify-between">
							<span class="text-sm text-green-600 font-medium">Probado por emprendedores</span>
							<a href="/descargas/presupuesto-ingresos-variables" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
								Descargar Excel
							</a>
						</div>
					</div>

					<!-- Template 3 -->
					<div class="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300">
						<div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
							<svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
							</svg>
						</div>
						<h3 class="text-xl font-bold text-gray-900 mb-3">
							Presupuesto Familiar Optimizado
						</h3>
						<p class="text-gray-600 mb-4">
							La estrategia de Carmen para maximizar un solo ingreso familiar. 
							Incluye planificador de gastos por prioridades y fondo educativo.
						</p>
						<div class="flex items-center justify-between">
							<span class="text-sm text-green-600 font-medium">Para familias</span>
							<button class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
								Descargar Excel
							</button>
						</div>
					</div>

					<!-- Template 4 -->
					<div class="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300">
						<div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
							<svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
							</svg>
						</div>
						<h3 class="text-xl font-bold text-gray-900 mb-3">
							Plan de Fondo de Emergencia
						</h3>
						<p class="text-gray-600 mb-4">
							Calculadora personalizada para determinar el tamaño ideal de tu fondo según tu situación específica. 
							Con cronograma de ahorro automático.
						</p>
						<div class="flex items-center justify-between">
							<span class="text-sm text-green-600 font-medium">Personalizable</span>
							<button class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
								Descargar Excel
							</button>
						</div>
					</div>

					<!-- Template 5 -->
					<div class="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300">
						<div class="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mb-4">
							<svg class="w-6 h-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
							</svg>
						</div>
						<h3 class="text-xl font-bold text-gray-900 mb-3">
							Tracker de Progreso Financiero
						</h3>
						<p class="text-gray-600 mb-4">
							Dashboard visual para seguir tu progreso hacia objetivos financieros. 
							Incluye gráficos automáticos y métricas clave.
						</p>
						<div class="flex items-center justify-between">
							<span class="text-sm text-green-600 font-medium">Visual e intuitivo</span>
							<button class="bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
								Descargar Excel
							</button>
						</div>
					</div>

					<!-- Template 6 -->
					<div class="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300">
						<div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
							<svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
							</svg>
						</div>
						<h3 class="text-xl font-bold text-gray-900 mb-3">
							Plan de Transición de Carrera
						</h3>
						<p class="text-gray-600 mb-4">
							La estrategia de Laura para mantener estabilidad financiera durante 8 meses de cambio profesional. 
							Incluye presupuesto de transición y plan de contingencia.
						</p>
						<div class="flex items-center justify-between">
							<span class="text-sm text-green-600 font-medium">Para cambios profesionales</span>
							<button class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
								Descargar Excel
							</button>
						</div>
					</div>
				</div>
			</div>

			<!-- Guides Section -->
			<div class="mb-16">
				<h2 class="text-3xl font-bold text-gray-900 text-center mb-12">
					Guías Completas
				</h2>
				
				<div class="grid grid-cols-1 md:grid-cols-2 gap-8">
					<!-- Guide 1 -->
					<div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-8 border border-blue-100">
						<h3 class="text-2xl font-bold text-gray-900 mb-4">
							Guía Completa de Refinanciamiento
						</h3>
						<p class="text-gray-700 mb-6">
							Todo lo que necesitas saber sobre refinanciamiento de deudas, basado en la experiencia de María 
							que ahorró €12,000 en intereses.
						</p>
						<ul class="space-y-2 mb-6">
							<li class="flex items-center text-gray-700">
								<svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								Cuándo refinanciar y cuándo no
							</li>
							<li class="flex items-center text-gray-700">
								<svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								Cómo mejorar tu score crediticio antes de aplicar
							</li>
							<li class="flex items-center text-gray-700">
								<svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								Comparación de prestamistas y términos
							</li>
							<li class="flex items-center text-gray-700">
								<svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								Calculadora de ahorros potenciales
							</li>
						</ul>
						<button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
							Descargar Guía PDF
						</button>
					</div>

					<!-- Guide 2 -->
					<div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-8 border border-green-100">
						<h3 class="text-2xl font-bold text-gray-900 mb-4">
							Finanzas para Emprendedores
						</h3>
						<p class="text-gray-700 mb-6">
							Estrategias específicas para manejar ingresos variables y separar finanzas personales del negocio, 
							basado en el sistema de Carlos.
						</p>
						<ul class="space-y-2 mb-6">
							<li class="flex items-center text-gray-700">
								<svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								Separación de finanzas personales y de negocio
							</li>
							<li class="flex items-center text-gray-700">
								<svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								Presupuesto por percentiles de ingresos
							</li>
							<li class="flex items-center text-gray-700">
								<svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								Estrategias fiscales para autónomos
							</li>
							<li class="flex items-center text-gray-700">
								<svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
								Diversificación de fuentes de ingreso
							</li>
						</ul>
						<button class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
							Descargar Guía PDF
						</button>
					</div>
				</div>
			</div>

			<!-- Tools Section -->
			<div class="mb-16">
				<h2 class="text-3xl font-bold text-gray-900 text-center mb-12">
					Herramientas Recomendadas
				</h2>
				
				<div class="bg-yellow-50 border border-yellow-200 rounded-xl p-8 mb-8">
					<div class="flex items-start">
						<div class="flex-shrink-0">
							<svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
							</svg>
						</div>
						<div class="ml-4">
							<h3 class="text-lg font-semibold text-yellow-900 mb-2">
								Transparencia sobre Afiliaciones
							</h3>
							<p class="text-yellow-800">
								Las herramientas que recomendamos son las que realmente han usado las personas en nuestras historias. 
								Algunos enlaces son de afiliación, lo que significa que podemos recibir una comisión si decides usarlas, 
								pero esto no afecta nuestras recomendaciones.
							</p>
						</div>
					</div>
				</div>
				
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					<!-- Tool 1 -->
					<div class="bg-white border border-gray-200 rounded-lg p-6">
						<h4 class="font-bold text-gray-900 mb-2">YNAB (You Need A Budget)</h4>
						<p class="text-sm text-gray-600 mb-3">Presupuesto</p>
						<p class="text-gray-700 mb-4">
							La app que María usó para visualizar su progreso de pago de deudas y mantener disciplina financiera.
						</p>
						<a href="/go/ynab" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium">
							Ver herramienta
							<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
							</svg>
						</a>
					</div>

					<!-- Tool 2 -->
					<div class="bg-white border border-gray-200 rounded-lg p-6">
						<h4 class="font-bold text-gray-900 mb-2">Holded</h4>
						<p class="text-sm text-gray-600 mb-3">Gestión Financiera</p>
						<p class="text-gray-700 mb-4">
							Software integral que Carlos usa para separar gastos automáticamente y generar reportes fiscales.
						</p>
						<a href="/go/holded" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium">
							Ver herramienta
							<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
							</svg>
						</a>
					</div>

					<!-- Tool 3 -->
					<div class="bg-white border border-gray-200 rounded-lg p-6">
						<h4 class="font-bold text-gray-900 mb-2">N26 Business</h4>
						<p class="text-sm text-gray-600 mb-3">Banca Digital</p>
						<p class="text-gray-700 mb-4">
							Cuenta bancaria digital que facilita la separación de finanzas y ofrece insights en tiempo real.
						</p>
						<a href="/go/n26-business" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium">
							Ver herramienta
							<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
							</svg>
						</a>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- CTA Section -->
	<section class="bg-gradient-to-r from-teal-600 to-cyan-600 py-16">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
			<h2 class="text-3xl font-bold text-white mb-4">
				¿Necesitas algo específico?
			</h2>
			<p class="text-xl text-teal-100 mb-8 max-w-2xl mx-auto">
				Si no encuentras la plantilla o guía que necesitas, podemos crear recursos personalizados 
				basados en tu situación específica.
			</p>
			<div class="flex flex-col sm:flex-row gap-4 justify-center">
				<a href="/contacto" class="bg-white text-teal-600 hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg transition-colors">
					Solicitar recurso personalizado
				</a>
				<a href="/historias" class="border-2 border-white text-white hover:bg-white hover:text-teal-600 font-semibold py-3 px-8 rounded-lg transition-colors">
					Ver historias relacionadas
				</a>
			</div>
		</div>
	</section>
</Layout>
