import { db, User, Session, eq, and, gt } from 'astro:db';
import bcrypt from 'bcryptjs';
import { SignJWT, jwtVerify } from 'jose';

const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'finanzas-reales-secret-key-2024'
);

export interface AuthUser {
  id: number;
  email: string;
  name: string;
  role: string;
}

export interface SessionData {
  userId: number;
  email: string;
  name: string;
  role: string;
  sessionId: string;
}

// Generar ID de sesión único
function generateSessionId(): string {
  return crypto.randomUUID();
}

// Crear JWT token
export async function createJWT(payload: SessionData): Promise<string> {
  return await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('7d')
    .sign(JWT_SECRET);
}

// Verificar JWT token
export async function verifyJWT(token: string): Promise<SessionData | null> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return payload as SessionData;
  } catch {
    return null;
  }
}

// Autenticar usuario
export async function authenticateUser(email: string, password: string): Promise<AuthUser | null> {
  try {
    const users = await db.select().from(User).where(eq(User.email, email));
    const user = users[0];
    
    if (!user) {
      return null;
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return null;
    }

    // Actualizar último login
    await db.update(User)
      .set({ lastLogin: new Date() })
      .where(eq(User.id, user.id));

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
    };
  } catch (error) {
    console.error('Error authenticating user:', error);
    return null;
  }
}

// Crear sesión
export async function createSession(user: AuthUser): Promise<string> {
  const sessionId = generateSessionId();
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 días

  try {
    await db.insert(Session).values({
      id: sessionId,
      userId: user.id,
      expiresAt,
      createdAt: new Date(),
    });

    const sessionData: SessionData = {
      userId: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      sessionId,
    };

    return await createJWT(sessionData);
  } catch (error) {
    console.error('Error creating session:', error);
    throw new Error('Failed to create session');
  }
}

// Verificar sesión
export async function verifySession(token: string): Promise<SessionData | null> {
  try {
    const payload = await verifyJWT(token);
    if (!payload) {
      return null;
    }

    // Verificar que la sesión existe en la base de datos y no ha expirado
    const sessions = await db.select()
      .from(Session)
      .where(
        and(
          eq(Session.id, payload.sessionId),
          eq(Session.userId, payload.userId),
          gt(Session.expiresAt, new Date())
        )
      );

    if (sessions.length === 0) {
      return null;
    }

    return payload;
  } catch (error) {
    console.error('Error verifying session:', error);
    return null;
  }
}

// Eliminar sesión (logout)
export async function destroySession(sessionId: string): Promise<void> {
  try {
    await db.delete(Session).where(eq(Session.id, sessionId));
  } catch (error) {
    console.error('Error destroying session:', error);
  }
}

// Limpiar sesiones expiradas
export async function cleanupExpiredSessions(): Promise<void> {
  try {
    await db.delete(Session).where(gt(new Date(), Session.expiresAt));
  } catch (error) {
    console.error('Error cleaning up expired sessions:', error);
  }
}

// Middleware de autenticación para rutas protegidas
export async function requireAuth(request: Request): Promise<SessionData | Response> {
  const authHeader = request.headers.get('Authorization');
  const cookieHeader = request.headers.get('Cookie');
  
  let token: string | null = null;

  // Buscar token en Authorization header
  if (authHeader && authHeader.startsWith('Bearer ')) {
    token = authHeader.substring(7);
  }
  
  // Buscar token en cookies
  if (!token && cookieHeader) {
    const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
      const [key, value] = cookie.trim().split('=');
      acc[key] = value;
      return acc;
    }, {} as Record<string, string>);
    
    token = cookies['auth-token'];
  }

  if (!token) {
    return new Response(JSON.stringify({ error: 'No authentication token provided' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  const session = await verifySession(token);
  if (!session) {
    return new Response(JSON.stringify({ error: 'Invalid or expired session' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  return session;
}

// Crear usuario administrador
export async function createAdminUser(email: string, password: string, name: string): Promise<AuthUser | null> {
  try {
    // Verificar si el usuario ya existe
    const existingUsers = await db.select().from(User).where(eq(User.email, email));
    if (existingUsers.length > 0) {
      throw new Error('User already exists');
    }

    const hashedPassword = await bcrypt.hash(password, 10);
    
    const result = await db.insert(User).values({
      email,
      password: hashedPassword,
      name,
      role: 'admin',
      createdAt: new Date(),
    }).returning();

    const user = result[0];
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
    };
  } catch (error) {
    console.error('Error creating admin user:', error);
    return null;
  }
}

// Obtener usuario por ID
export async function getUserById(id: number): Promise<AuthUser | null> {
  try {
    const users = await db.select().from(User).where(eq(User.id, id));
    const user = users[0];
    
    if (!user) {
      return null;
    }

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
    };
  } catch (error) {
    console.error('Error getting user by ID:', error);
    return null;
  }
}
