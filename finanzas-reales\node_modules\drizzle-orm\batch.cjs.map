{"version": 3, "sources": ["../src/batch.ts"], "sourcesContent": ["import type { Dialect } from './column-builder.ts';\nimport type { RunnableQuery } from './runnable-query.ts';\n\nexport type BatchItem<TDialect extends Dialect = Dialect> = RunnableQuery<any, TDialect>;\n\nexport type BatchResponse<T extends BatchItem[] | readonly BatchItem[]> = {\n\t[K in keyof T]: T[K]['_']['result'];\n};\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}