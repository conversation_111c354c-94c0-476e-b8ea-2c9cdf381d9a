{"version": 3, "sources": ["../../../src/gel-core/columns/all.ts"], "sourcesContent": ["import { bigint } from './bigint.ts';\nimport { bigintT } from './bigintT.ts';\nimport { boolean } from './boolean.ts';\nimport { bytes } from './bytes.ts';\nimport { customType } from './custom.ts';\nimport { dateDuration } from './date-duration.ts';\nimport { decimal } from './decimal.ts';\nimport { doublePrecision } from './double-precision.ts';\nimport { duration } from './duration.ts';\nimport { integer } from './integer.ts';\nimport { json } from './json.ts';\nimport { localDate } from './localdate.ts';\nimport { localTime } from './localtime.ts';\nimport { real } from './real.ts';\nimport { relDuration } from './relative-duration.ts';\nimport { smallint } from './smallint.ts';\nimport { text } from './text.ts';\nimport { timestamp } from './timestamp.ts';\nimport { timestamptz } from './timestamptz.ts';\nimport { uuid } from './uuid.ts';\n\n// TODO add\nexport function getGelColumnBuilders() {\n\treturn {\n\t\tlocalDate,\n\t\tlocalTime,\n\t\tdecimal,\n\t\tdateDuration,\n\t\tbigintT,\n\t\tduration,\n\t\trelDuration,\n\t\tbytes,\n\t\tcustomType,\n\t\tbigint,\n\t\tboolean,\n\t\tdoublePrecision,\n\t\tinteger,\n\t\tjson,\n\t\treal,\n\t\tsmallint,\n\t\ttext,\n\t\ttimestamptz,\n\t\tuuid,\n\t\ttimestamp,\n\t};\n}\n\nexport type GelColumnsBuilders = ReturnType<typeof getGelColumnBuilders>;\n"], "mappings": "AAAA,SAAS,cAAc;AACvB,SAAS,eAAe;AACxB,SAAS,eAAe;AACxB,SAAS,aAAa;AACtB,SAAS,kBAAkB;AAC3B,SAAS,oBAAoB;AAC7B,SAAS,eAAe;AACxB,SAAS,uBAAuB;AAChC,SAAS,gBAAgB;AACzB,SAAS,eAAe;AACxB,SAAS,YAAY;AACrB,SAAS,iBAAiB;AAC1B,SAAS,iBAAiB;AAC1B,SAAS,YAAY;AACrB,SAAS,mBAAmB;AAC5B,SAAS,gBAAgB;AACzB,SAAS,YAAY;AACrB,SAAS,iBAAiB;AAC1B,SAAS,mBAAmB;AAC5B,SAAS,YAAY;AAGd,SAAS,uBAAuB;AACtC,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;", "names": []}