{"version": 3, "sources": ["../../../src/mysql-core/columns/binary.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { MySqlColumn, MySqlColumnBuilder } from './common.ts';\n\nexport type MySqlBinaryBuilderInitial<TName extends string> = MySqlBinaryBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'MySqlBinary';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlBinaryBuilder<T extends ColumnBuilderBaseConfig<'string', 'MySqlBinary'>> extends MySqlColumnBuilder<\n\tT,\n\tMySqlBinaryConfig\n> {\n\tstatic override readonly [entityKind]: string = 'MySqlBinaryBuilder';\n\n\tconstructor(name: T['name'], length: number | undefined) {\n\t\tsuper(name, 'string', 'MySqlBinary');\n\t\tthis.config.length = length;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlBinary<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlBinary<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class MySqlBinary<T extends ColumnBaseConfig<'string', 'MySqlBinary'>> extends MySqlColumn<\n\tT,\n\tMySqlBinaryConfig\n> {\n\tstatic override readonly [entityKind]: string = 'MySqlBinary';\n\n\tlength: number | undefined = this.config.length;\n\n\toverride mapFromDriverValue(value: string | Buffer | Uint8Array): string {\n\t\tif (typeof value === 'string') return value;\n\t\tif (Buffer.isBuffer(value)) return value.toString();\n\n\t\tconst str: string[] = [];\n\t\tfor (const v of value) {\n\t\t\tstr.push(v === 49 ? '1' : '0');\n\t\t}\n\n\t\treturn str.join('');\n\t}\n\n\tgetSQLType(): string {\n\t\treturn this.length === undefined ? `binary` : `binary(${this.length})`;\n\t}\n}\n\nexport interface MySqlBinaryConfig {\n\tlength?: number;\n}\n\nexport function binary(): MySqlBinaryBuilderInitial<''>;\nexport function binary(\n\tconfig?: MySqlBinaryConfig,\n): MySqlBinaryBuilderInitial<''>;\nexport function binary<TName extends string>(\n\tname: TName,\n\tconfig?: MySqlBinaryConfig,\n): MySqlBinaryBuilderInitial<TName>;\nexport function binary(a?: string | MySqlBinaryConfig, b: MySqlBinaryConfig = {}) {\n\tconst { name, config } = getColumnNameAndConfig<MySqlBinaryConfig>(a, b);\n\treturn new MySqlBinaryBuilder(name, config.length);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,8BAA8B;AACvC,SAAS,aAAa,0BAA0B;AAWzC,MAAM,2BAAuF,mBAGlG;AAAA,EACD,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAA4B;AACxD,UAAM,MAAM,UAAU,aAAa;AACnC,SAAK,OAAO,SAAS;AAAA,EACtB;AAAA;AAAA,EAGS,MACR,OAC+C;AAC/C,WAAO,IAAI,YAA6C,OAAO,KAAK,MAA8C;AAAA,EACnH;AACD;AAEO,MAAM,oBAAyE,YAGpF;AAAA,EACD,QAA0B,UAAU,IAAY;AAAA,EAEhD,SAA6B,KAAK,OAAO;AAAA,EAEhC,mBAAmB,OAA6C;AACxE,QAAI,OAAO,UAAU;AAAU,aAAO;AACtC,QAAI,OAAO,SAAS,KAAK;AAAG,aAAO,MAAM,SAAS;AAElD,UAAM,MAAgB,CAAC;AACvB,eAAW,KAAK,OAAO;AACtB,UAAI,KAAK,MAAM,KAAK,MAAM,GAAG;AAAA,IAC9B;AAEA,WAAO,IAAI,KAAK,EAAE;AAAA,EACnB;AAAA,EAEA,aAAqB;AACpB,WAAO,KAAK,WAAW,SAAY,WAAW,UAAU,KAAK,MAAM;AAAA,EACpE;AACD;AAcO,SAAS,OAAO,GAAgC,IAAuB,CAAC,GAAG;AACjF,QAAM,EAAE,MAAM,OAAO,IAAI,uBAA0C,GAAG,CAAC;AACvE,SAAO,IAAI,mBAAmB,MAAM,OAAO,MAAM;AAClD;", "names": []}