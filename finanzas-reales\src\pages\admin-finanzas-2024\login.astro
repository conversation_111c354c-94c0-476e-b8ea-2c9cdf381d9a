---
import { authenticateUser, createSession } from '../../lib/auth';

let error = '';
let success = false;

if (Astro.request.method === 'POST') {
  try {
    const formData = await Astro.request.formData();
    const email = formData.get('email') as string;
    const password = formData.get('password') as string;

    if (!email || !password) {
      error = 'Email y contraseña son requeridos';
    } else {
      const user = await authenticateUser(email, password);
      
      if (user) {
        const token = await createSession(user);
        
        // Establecer cookie de autenticación
        Astro.cookies.set('auth-token', token, {
          httpOnly: true,
          secure: true,
          sameSite: 'strict',
          maxAge: 7 * 24 * 60 * 60, // 7 días
          path: '/'
        });
        
        success = true;
        // Redirigir al dashboard después de un breve delay
        return Astro.redirect('/admin-finanzas-2024/dashboard');
      } else {
        error = 'Credenciales inválidas';
      }
    }
  } catch (e) {
    console.error('Login error:', e);
    error = 'Error interno del servidor';
  }
}
---

<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Admin Login - FinanzasReales</title>
  <style>
    /* Importar Tailwind CSS */
    @import url('https://cdn.tailwindcss.com/3.4.0');

    /* Gradiente personalizado */
    .admin-gradient {
      background: linear-gradient(135deg, #0f766e 0%, #059669 50%, #10b981 100%);
    }

    /* Animaciones suaves */
    .fade-in {
      animation: fadeIn 0.6s ease-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    /* Efectos de hover mejorados */
    .btn-primary {
      @apply bg-emerald-600 hover:bg-emerald-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl;
    }

    .input-field {
      @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200 bg-white/90 backdrop-blur-sm;
    }

    .card-glass {
      @apply bg-white/95 backdrop-blur-sm shadow-2xl rounded-2xl border border-white/20;
    }
  </style>
</head>
<body class="min-h-screen admin-gradient flex items-center justify-center py-8 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8 fade-in">
    <!-- Logo y Header -->
    <div class="text-center">
      <div class="mx-auto h-16 w-16 flex items-center justify-center rounded-2xl bg-white/20 backdrop-blur-sm border border-white/30 shadow-lg">
        <svg class="h-10 w-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
        </svg>
      </div>
      <h1 class="mt-6 text-center text-3xl font-bold text-white">
        Panel de Administración
      </h1>
      <p class="mt-2 text-center text-emerald-100 font-medium">
        FinanzasReales
      </p>
      <p class="text-center text-sm text-emerald-200/80">
        Acceso Restringido
      </p>
    </div>

    <!-- Formulario de Login -->
    <div class="card-glass p-8">
      <form class="space-y-6" method="POST">
        <div class="space-y-4">
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
              Correo Electrónico
            </label>
            <input
              id="email"
              name="email"
              type="email"
              autocomplete="email"
              required
              class="input-field"
              placeholder="<EMAIL>"
            >
          </div>
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
              Contraseña
            </label>
            <input
              id="password"
              name="password"
              type="password"
              autocomplete="current-password"
              required
              class="input-field"
              placeholder="••••••••"
            >
          </div>
        </div>

        {error && (
          <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg flex items-center">
            <svg class="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            {error}
          </div>
        )}

        {success && (
          <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg flex items-center">
            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Login exitoso. Redirigiendo...
          </div>
        )}

        <button
          type="submit"
          class="btn-primary w-full flex justify-center items-center"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
          </svg>
          Iniciar Sesión
        </button>
      </form>


      <!-- Información de credenciales -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <div>
            <p class="text-sm font-medium text-blue-800">Credenciales por defecto:</p>
            <p class="text-xs text-blue-600"><EMAIL> / admin123!</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Link de regreso -->
    <div class="text-center mt-6">
      <a href="/" class="inline-flex items-center text-white/80 hover:text-white text-sm font-medium transition-colors">
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Volver al sitio principal
      </a>
    </div>
  </div>

  <script>
    // Auto-redirect si ya está autenticado
    if (document.cookie.includes('auth-token=')) {
      window.location.href = '/admin-finanzas-2024/dashboard';
    }

    // Animación de entrada
    document.addEventListener('DOMContentLoaded', function() {
      const form = document.querySelector('.fade-in');
      form.style.opacity = '0';
      form.style.transform = 'translateY(20px)';

      setTimeout(() => {
        form.style.transition = 'all 0.6s ease-out';
        form.style.opacity = '1';
        form.style.transform = 'translateY(0)';
      }, 100);
    });
  </script>
</body>
</html>
