---
import { authenticateUser, createSession } from '../../lib/auth';

let error = '';
let success = false;

if (Astro.request.method === 'POST') {
  try {
    const formData = await Astro.request.formData();
    const email = formData.get('email') as string;
    const password = formData.get('password') as string;

    if (!email || !password) {
      error = 'Email y contraseña son requeridos';
    } else {
      const user = await authenticateUser(email, password);
      
      if (user) {
        const token = await createSession(user);
        
        // Establecer cookie de autenticación
        Astro.cookies.set('auth-token', token, {
          httpOnly: true,
          secure: true,
          sameSite: 'strict',
          maxAge: 7 * 24 * 60 * 60, // 7 días
          path: '/'
        });
        
        success = true;
        // Redirigir al dashboard después de un breve delay
        return Astro.redirect('/admin-finanzas-2024/dashboard');
      } else {
        error = 'Credenciales inválidas';
      }
    }
  } catch (e) {
    console.error('Login error:', e);
    error = 'Error interno del servidor';
  }
}
---

<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Admin Login - FinanzasReales</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  </style>
</head>
<body class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <div>
      <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-white">
        <svg class="h-8 w-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
        </svg>
      </div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-white">
        Panel de Administración
      </h2>
      <p class="mt-2 text-center text-sm text-indigo-100">
        FinanzasReales - Acceso Restringido
      </p>
    </div>
    
    <form class="mt-8 space-y-6" method="POST">
      <div class="rounded-md shadow-sm -space-y-px">
        <div>
          <label for="email" class="sr-only">Email</label>
          <input 
            id="email" 
            name="email" 
            type="email" 
            autocomplete="email" 
            required 
            class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm" 
            placeholder="Email"
          >
        </div>
        <div>
          <label for="password" class="sr-only">Contraseña</label>
          <input 
            id="password" 
            name="password" 
            type="password" 
            autocomplete="current-password" 
            required 
            class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm" 
            placeholder="Contraseña"
          >
        </div>
      </div>

      {error && (
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {success && (
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          Login exitoso. Redirigiendo...
        </div>
      )}

      <div>
        <button 
          type="submit" 
          class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
        >
          <span class="absolute left-0 inset-y-0 flex items-center pl-3">
            <svg class="h-5 w-5 text-indigo-500 group-hover:text-indigo-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
            </svg>
          </span>
          Iniciar Sesión
        </button>
      </div>

      <div class="text-center">
        <p class="text-xs text-indigo-100">
          Credenciales por defecto: <EMAIL> / admin123!
        </p>
      </div>
    </form>

    <div class="text-center">
      <a href="/" class="text-indigo-100 hover:text-white text-sm">
        ← Volver al sitio principal
      </a>
    </div>
  </div>

  <script>
    // Auto-redirect si ya está autenticado
    if (document.cookie.includes('auth-token=')) {
      window.location.href = '/admin-finanzas-2024/dashboard';
    }
  </script>
</body>
</html>
