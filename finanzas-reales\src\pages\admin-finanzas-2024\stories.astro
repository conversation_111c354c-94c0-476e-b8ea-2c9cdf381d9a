---
import { verifySession } from '../../lib/auth';
import AdminLayout from '../../layouts/AdminLayout.astro';

// Verificar autenticación
const authToken = Astro.cookies.get('auth-token')?.value;
if (!authToken) {
  return Astro.redirect('/admin-finanzas-2024/login');
}

const session = await verifySession(authToken);
if (!session) {
  Astro.cookies.delete('auth-token');
  return Astro.redirect('/admin-finanzas-2024/login');
}
---

<AdminLayout title="Gestión de Historias - Admin FinanzasReales">
  <!-- Navigation -->
  <nav class="admin-nav">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center space-x-4">
          <a href="/admin-finanzas-2024/dashboard" class="text-xl font-semibold text-gray-900">
            FinanzasReales Admin
          </a>
          <span class="text-gray-400">|</span>
          <span class="text-gray-600">Historias</span>
        </div>
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-700">
            {session.name}
          </span>
          <a 
            href="/admin-finanzas-2024/logout" 
            class="text-sm text-red-600 hover:text-red-800"
          >
            Cerrar Sesión
          </a>
        </div>
      </div>
    </div>
  </nav>

  <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
    <!-- Page header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">
        Gestión de Historias Financieras
      </h1>
      <p class="text-gray-600 text-lg">
        Administra todas las historias financieras reales
      </p>
    </div>

    <!-- Coming Soon Message -->
    <div class="bg-white rounded-xl shadow-lg p-8 text-center">
      <div class="max-w-md mx-auto">
        <div class="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
          </svg>
        </div>
        <h2 class="text-xl font-bold text-gray-900 mb-2">
          Gestión de Historias
        </h2>
        <p class="text-gray-600 mb-6">
          Esta funcionalidad está en desarrollo. Pronto podrás gestionar todas las historias financieras desde aquí.
        </p>
        <div class="space-y-3">
          <p class="text-sm text-gray-500">
            <strong>Funcionalidades próximas:</strong>
          </p>
          <ul class="text-sm text-gray-500 space-y-1">
            <li>• Crear nuevas historias financieras</li>
            <li>• Editar historias existentes</li>
            <li>• Gestionar estados (borrador/publicado)</li>
            <li>• Organizar por perfiles financieros</li>
          </ul>
        </div>
        <div class="mt-6">
          <a 
            href="/admin-finanzas-2024/dashboard"
            class="inline-flex items-center px-4 py-2 bg-emerald-600 hover:bg-emerald-700 text-white font-medium rounded-lg transition-colors"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Volver al Dashboard
          </a>
        </div>
      </div>
    </div>
  </div>
</AdminLayout>
